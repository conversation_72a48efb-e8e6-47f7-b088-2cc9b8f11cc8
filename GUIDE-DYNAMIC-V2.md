# Guide Catalogue Dynamic V2

## 🎯 Objectif
Générer automatiquement un catalogue InDesign avec le design Dynamic Access V2 à partir de votre fichier Excel existant, avec support arabe et design moderne.

## ✨ Nouvelles Fonctionnalités V2

### 🏷️ Titres de Catégorie Automatiques
- **Nom de la feuille Excel** utilisé comme titre de catégorie
- **Design moderne** avec couleur gris foncé
- **Motifs décoratifs** sous le titre (lignes et carrés colorés)

### 🖼️ Logo Intelligent
- **Utilise logo.png** s'il existe dans le dossier `images/`
- **Fallback automatique** vers "Dynamic Access" si l'image n'existe pas
- **Aucun prix** affiché dans le logo

### 🌍 Support Arabe Complet
- **Direction RTL** (Right-to-Left) pour le texte arabe
- **Caractères arabes** supportés nativement
- **Applications véhicules** en arabe avec alignement automatique

### 🎨 Design Moderne
- **Titres de catégorie** avec style gris foncé
- **Motifs décoratifs** modernes (lignes rouges, carrés colorés)
- **Layout optimisé** pour l'impression professionnelle

## 📋 Prérequis
- Node.js installé
- Adobe InDesign
- Votre fichier Excel `excel/catalogue.xlsx`
- Logo `images/logo.png` (optionnel)

## 📁 Structure Excel Requise
Votre fichier `excel/catalogue.xlsx` doit contenir ces colonnes :
- **Colonne A** : Référence (ex: DFA0011)
- **Colonne B** : Applications véhicules
- **Colonne C** : Correspondances (format: RENAULT:123, SINFA:456, MECAFILTRE:789)
- **Colonne D** : Nom du fichier image (ex: DFA0011.jpg)

## 🚀 Utilisation

### 1. Préparer les données
- Placez votre fichier Excel dans `excel/catalogue.xlsx`
- Placez vos images dans le dossier `images/`
- Placez `logo.png` dans `images/` (optionnel)

### 2. Générer le catalogue
```bash
node lance-dynamic-v2.js
```

### 3. Créer le catalogue dans InDesign
1. Ouvrir Adobe InDesign
2. **Fichier** > **Scripts** > **Exécuter le script**
3. Sélectionner `output/generate_catalogue_dynamic_v2.jsx`
4. Le catalogue sera créé automatiquement

## 🎨 Design Appliqué

### En-tête de Page
- **Logo Dynamic** : Zone grise avec logo.png ou texte "Dynamic Access"
- **Bandeau rouge** : "RENAULT - DACIA - CITROEN - PEUGEOT" + texte arabe

### Titres de Catégorie
- **Nom de la feuille Excel** : "Filtre à Air Européennes"
- **Couleur** : Gris foncé (80%)
- **Style** : Bold, 18pt
- **Motifs** : Ligne rouge + carrés décoratifs

### Layout des Produits
- **6 produits par page** en grille 2x3
- **Référence Dynamic** : En orange, taille 14pt, Bold
- **Spécifications** : Extraites automatiquement des correspondances
- **Images** : Intégrées automatiquement
- **Applications** : Liste des véhicules compatibles (RTL pour l'arabe)

### Extraction Automatique
Le système extrait automatiquement de vos correspondances :
- **OEM** : Référence RENAULT
- **SINFA** : Référence SINFA
- **MECAFILTRE** : Référence MECAFILTRE
- **KNECHT** : Référence KNECHT
- **MISFAT** : Référence MISFAT
- **MANN** : Référence MANN
- **FILTRON** : Référence FILTRON

## 📁 Fichiers Utilisés

```
Dynamic-Access/
├── excel/catalogue.xlsx              # Votre fichier Excel
├── images/                           # Vos images des filtres
│   └── logo.png                     # Logo Dynamic (optionnel)
├── generate-catalogue-dynamic-v2.js  # Générateur V2
├── lance-dynamic-v2.js              # Lanceur V2
└── output/
    └── generate_catalogue_dynamic_v2.jsx  # Script InDesign V2
```

## 🔧 Préparation des Images

1. **Images produits** : Placer dans `images/` avec les noms de votre Excel
2. **Logo** : Placer `logo.png` dans `images/` (optionnel)
3. **Format recommandé** : JPG ou PNG
4. **Résolution** : minimum 300 DPI pour l'impression

## 📊 Exemple de Données

Votre Excel ressemble à ceci :
```
Référence    | Applications                    | Correspondances                                    | Images
DFA0011      | Renault Clio 1.9 D (64 HP)...  | RENAULT: 7701034705 | ASZ: ASZ1011 | SINFA: 22877... | DFA0011.jpg
```

Le système extrait automatiquement :
- **Référence** : DFA0011
- **OEM** : 7701034705
- **SINFA** : 22877
- **Applications** : Renault Clio 1.9 D (64 HP)...
- **Images** : DFA0011.jpg
- **Catégorie** : Nom de la feuille Excel

## 🎯 Résultat Final

Le catalogue généré aura :
- ✅ En-tête avec logo Dynamic Access (logo.png ou texte)
- ✅ Titre de catégorie moderne avec le nom de la feuille Excel
- ✅ 6 filtres par page avec toutes leurs spécifications
- ✅ Images intégrées automatiquement
- ✅ Applications véhicules en arabe avec direction RTL
- ✅ Numérotation des pages avec éléments décoratifs
- ✅ Design moderne et professionnel
- ✅ Prêt pour l'impression

## 🐛 Dépannage

### Erreur "Fichier Excel non trouvé"
- Vérifier que `excel/catalogue.xlsx` existe
- Vérifier les permissions du fichier

### Erreur "Images non trouvées"
- Vérifier que les images sont dans `images/`
- Vérifier que les noms correspondent à la colonne "Images"

### Problème d'extraction des correspondances
- Vérifier le format des correspondances
- Utiliser des séparateurs : `|` ou `,` ou `;`
- Format attendu : `RENAULT: 123 | SINFA: 456 | MANN: 789`

### Problème avec le texte arabe
- Vérifier qu'InDesign supporte l'arabe
- Vérifier que la police supporte les caractères arabes
- Le système applique automatiquement la direction RTL

## 📞 Support

Pour toute question :
1. Vérifier ce guide
2. Consulter les logs d'erreur
3. Vérifier la structure de votre Excel
4. Tester avec quelques produits d'abord
5. Vérifier que le logo.png est bien placé

---

**🎉 Votre Excel est maintenant prêt à générer un catalogue moderne avec le design Dynamic Access V2 !**
