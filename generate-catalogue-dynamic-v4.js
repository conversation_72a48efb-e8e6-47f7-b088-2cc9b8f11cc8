const XLSX = require('xlsx');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class CatalogueDynamicV4Generator {
    constructor() {
        this.excelPath = path.join(__dirname, 'excel', 'catalogue.xlsx');
        this.outputPath = path.join(__dirname, 'output');
        this.imagesPath = path.join(__dirname, 'images');
    }

    async generateCatalogue() {
        try {
            console.log(chalk.blue('🚀 Début de la génération du catalogue Dynamic V4...'));
            
            // Vérifier que le fichier Excel existe
            if (!await fs.pathExists(this.excelPath)) {
                throw new Error('Fichier Excel non trouvé. Veuillez créer le fichier excel/catalogue.xlsx');
            }

            // Lire le fichier Excel
            const products = await this.readExcelFile(this.excelPath);
            
            // Générer le fichier InDesign
            await this.generateInDesignFile(products);
            
            console.log(chalk.green('🎉 Catalogue Dynamic V4 généré avec succès !'));
            console.log(chalk.blue('📁 Fichier créé dans le dossier output/'));
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de la génération :'), error.message);
        }
    }

    async readExcelFile(filePath) {
        try {
            const workbook = XLSX.readFile(filePath);
            const sheetNames = workbook.SheetNames;
            
            if (sheetNames.length === 0) {
                throw new Error('Aucune feuille trouvée dans le fichier Excel');
            }

            const allProducts = [];
            
            // Traiter chaque feuille
            for (const sheetName of sheetNames) {
                console.log(`📋 Traitement de la feuille: ${sheetName}`);
                
                const worksheet = workbook.Sheets[sheetName];
                
                // Convertir en JSON avec gestion des en-têtes
                const data = XLSX.utils.sheet_to_json(worksheet, { 
                    header: 1,
                    defval: '' 
                });
                
                if (data.length < 2) {
                    console.log(`⚠️ Feuille "${sheetName}" ignorée - pas assez de données`);
                    continue;
                }
                
                const headers = data[0];
                const products = [];
                
                // Traiter chaque ligne de données
                for (let i = 1; i < data.length; i++) {
                    const row = data[i];
                    if (row.length === 0 || !row[0]) continue; // Ignorer les lignes vides
                    
                    // Extraire les données selon la structure existante
                    const reference = this.normalizeString(row[0] || ''); // Référence
                    const applications = this.normalizeString(row[1] || ''); // Applications
                    const correspondances = this.normalizeString(row[2] || ''); // Correspondances
                    const images = this.normalizeString(row[3] || ''); // Images
                    
                    // Parser les correspondances pour extraire les différentes références
                    const parsedCorrespondances = this.parseCorrespondances(correspondances);
                    
                    const product = {
                        Référence: reference,
                        OEM: parsedCorrespondances.OEM || '',
                        SINFA: parsedCorrespondances.SINFA || '',
                        MECAFILTRE: parsedCorrespondances.MECAFILTRE || '',
                        KNECHT: parsedCorrespondances.KNECHT || '',
                        MISFAT: parsedCorrespondances.MISFAT || '',
                        MANN: parsedCorrespondances.MANN || '',
                        FILTRON: parsedCorrespondances.FILTRON || '',
                        Applications: applications,
                        Images: images,
                        Catégorie: sheetName // Ajouter le nom de la feuille comme catégorie
                    };
                    
                    // Vérifier que la référence n'est pas vide
                    if (product.Référence.trim()) {
                        products.push(product);
                    }
                }
                
                // Ajouter les produits de cette feuille à la liste générale
                if (products.length > 0) {
                    allProducts.push({
                        catégorie: sheetName,
                        produits: products
                    });
                    console.log(`✅ ${products.length} produits chargés depuis la feuille "${sheetName}"`);
                }
            }
            
            console.log(`✅ Total: ${allProducts.reduce((sum, cat) => sum + cat.produits.length, 0)} produits chargés depuis ${allProducts.length} feuilles`);
            return allProducts;
            
        } catch (error) {
            console.error('❌ Erreur lors de la lecture du fichier Excel:', error.message);
            throw error;
        }
    }
    
    // Fonction pour parser les correspondances
    parseCorrespondances(correspondances) {
        const result = {};
        
        if (!correspondances) return result;
        
        // Diviser par les séparateurs possibles
        const parts = correspondances.split(/[|,;]/);
        
        parts.forEach(part => {
            const trimmed = part.trim();
            if (!trimmed) return;
            
            // Chercher les patterns de correspondance
            if (trimmed.includes('RENAULT:')) {
                result.OEM = trimmed.replace('RENAULT:', '').trim();
            } else if (trimmed.includes('ASZ:')) {
                // ASZ est déjà dans la référence principale
            } else if (trimmed.includes('SINFA:')) {
                result.SINFA = trimmed.replace('SINFA:', '').trim();
            } else if (trimmed.includes('MECAFILTRE:')) {
                result.MECAFILTRE = trimmed.replace('MECAFILTRE:', '').trim();
            } else if (trimmed.includes('KNECHT:')) {
                result.KNECHT = trimmed.replace('KNECHT:', '').trim();
            } else if (trimmed.includes('MISFAT:')) {
                result.MISFAT = trimmed.replace('MISFAT:', '').trim();
            } else if (trimmed.includes('MANN:')) {
                result.MANN = trimmed.replace('MANN:', '').trim();
            } else if (trimmed.includes('FILTRON:')) {
                result.FILTRON = trimmed.replace('FILTRON:', '').trim();
            }
        });
        
        return result;
    }
    
    // Fonction pour normaliser les chaînes de caractères
    normalizeString(str) {
        if (!str) return '';
        
        // Convertir en chaîne
        let normalized = str.toString();
        
        // Nettoyer les caractères spéciaux
        normalized = normalized.replace(/[\r\n\t\f\v]/g, ' ');
        normalized = normalized.replace(/\s+/g, ' ');
        normalized = normalized.trim();
        
        return normalized;
    }

    async generateInDesignFile(categorizedProducts) {
        const scriptContent = this.createInDesignScript(categorizedProducts);
        const outputFile = path.join(this.outputPath, 'generate_catalogue_dynamic_v4.jsx');
        
        await fs.ensureDir(this.outputPath);
        await fs.writeFile(outputFile, scriptContent, 'utf8');
        
        console.log(`📄 Script InDesign généré: ${outputFile}`);
    }

    createInDesignScript(categorizedProducts) {
        return `// Script InDesign pour Catalogue Dynamic V4
// Généré automatiquement pour votre structure Excel existante
// Version V4 avec 8 produits par page et données correctes

// Fonction pour créer un nouveau document
function createDocument() {
    var doc = app.documents.add();
    doc.documentPreferences.pageWidth = "210mm";
    doc.documentPreferences.pageHeight = "297mm";
    doc.documentPreferences.facingPages = false;
    doc.documentPreferences.pageOrientation = PageOrientation.PORTRAIT;
    
    // Créer les couleurs nécessaires
    createColors(doc);
    
    return doc;
}

// Fonction pour créer les couleurs
function createColors(doc) {
    // Couleur rouge pour les bandeaux
    try {
        var redColor = doc.colors.itemByName("Rouge");
        if (!redColor || redColor.isValid === false) {
            redColor = doc.colors.add();
            redColor.name = "Rouge";
            redColor.colorValue = [0, 100, 100, 0]; // Rouge
        }
    } catch (e) {
        redColor = doc.colors.add();
        redColor.name = "Rouge";
        redColor.colorValue = [0, 100, 100, 0];
    }
    
    // Couleur orange pour les références
    try {
        var orangeColor = doc.colors.itemByName("Orange");
        if (!orangeColor || orangeColor.isValid === false) {
            orangeColor = doc.colors.add();
            orangeColor.name = "Orange";
            orangeColor.colorValue = [0, 50, 100, 0]; // Orange
        }
    } catch (e) {
        orangeColor = doc.colors.add();
        orangeColor.name = "Orange";
        orangeColor.colorValue = [0, 50, 100, 0];
    }
    
    // Couleur gris foncé pour le logo
    try {
        var darkGrayColor = doc.colors.itemByName("GrisFonce");
        if (!darkGrayColor || darkGrayColor.isValid === false) {
            darkGrayColor = doc.colors.add();
            darkGrayColor.name = "GrisFonce";
            darkGrayColor.colorValue = [0, 0, 0, 80]; // Gris 80%
        }
    } catch (e) {
        darkGrayColor = doc.colors.add();
        darkGrayColor.name = "GrisFonce";
        darkGrayColor.colorValue = [0, 0, 0, 80];
    }
    
    // Couleur gris foncé pour les titres de catégorie
    try {
        var categoryGrayColor = doc.colors.itemByName("GrisCategorie");
        if (!categoryGrayColor || categoryGrayColor.isValid === false) {
            categoryGrayColor = doc.colors.add();
            categoryGrayColor.name = "GrisCategorie";
            categoryGrayColor.colorValue = [0, 0, 0, 80]; // Gris 80%
        }
    } catch (e) {
        categoryGrayColor = doc.colors.add();
        categoryGrayColor.name = "GrisCategorie";
        categoryGrayColor.colorValue = [0, 0, 0, 80];
    }
}

// Fonction pour créer les styles
function createStyles(doc) {
    // Style pour le titre principal
    var titleStyle = doc.paragraphStyles.add();
    titleStyle.name = "TitrePrincipal";
    titleStyle.appliedFont = app.fonts.itemByName("Arial");
    titleStyle.fontStyle = "Bold";
    titleStyle.pointSize = 24;
    titleStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les références Dynamic
    var refStyle = doc.paragraphStyles.add();
    refStyle.name = "ReferenceDynamic";
    refStyle.appliedFont = app.fonts.itemByName("Arial");
    refStyle.fontStyle = "Bold";
    refStyle.pointSize = 14;
    refStyle.fillColor = doc.colors.itemByName("Orange");
    
    // Style pour les spécifications
    var specStyle = doc.paragraphStyles.add();
    specStyle.name = "Specifications";
    specStyle.appliedFont = app.fonts.itemByName("Arial");
    specStyle.pointSize = 8;
    specStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les applications
    var appStyle = doc.paragraphStyles.add();
    appStyle.name = "Applications";
    appStyle.appliedFont = app.fonts.itemByName("Arial");
    appStyle.pointSize = 7;
    appStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour le bandeau rouge
    var bandeauStyle = doc.paragraphStyles.add();
    bandeauStyle.name = "BandeauRouge";
    bandeauStyle.appliedFont = app.fonts.itemByName("Arial");
    bandeauStyle.fontStyle = "Bold";
    bandeauStyle.pointSize = 12;
    bandeauStyle.fillColor = doc.colors.itemByName("Paper");
    
    // Style pour les titres de catégorie (gris foncé, moderne)
    var categoryStyle = doc.paragraphStyles.add();
    categoryStyle.name = "TitreCategorie";
    categoryStyle.appliedFont = app.fonts.itemByName("Arial");
    categoryStyle.fontStyle = "Bold";
    categoryStyle.pointSize = 18;
    categoryStyle.fillColor = doc.colors.itemByName("GrisCategorie");
    
    return {
        titleStyle: titleStyle,
        refStyle: refStyle,
        specStyle: specStyle,
        appStyle: appStyle,
        bandeauStyle: bandeauStyle,
        categoryStyle: categoryStyle
    };
}

// Fonction pour créer l'en-tête de page
function createPageHeader(page, styles) {
    // Logo Dynamic (zone gris foncé)
    var logoFrame = page.rectangles.add();
    logoFrame.geometricBounds = [10, 10, 30, 80];
    logoFrame.fillColor = doc.colors.itemByName("GrisFonce");
    
    // Image logo.png - ZOOMÉE pour remplir le frame
    try {
        var logoPath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/logo.png";
        var logoFile = new File(logoPath);
        if (logoFile.exists) {
            var logoImageFrame = page.rectangles.add();
            logoImageFrame.geometricBounds = [12, 15, 28, 75];
            logoImageFrame.place(File(logoPath));
            // ZOOMER l'image pour remplir complètement le frame
            logoImageFrame.fit(FittingOptions.FILL_FRAME);
            logoImageFrame.fit(FittingOptions.CENTER_CONTENT);
        } else {
            // Texte Dynamic Access si l'image n'existe pas
            var logoText = page.textFrames.add();
            logoText.geometricBounds = [12, 15, 28, 75];
            logoText.contents = "Dynamic\\nAccess";
            logoText.paragraphs[0].appliedParagraphStyle = styles.titleStyle;
            logoText.paragraphs[0].justification = Justification.CENTER_ALIGN;
            logoText.paragraphs[0].fillColor = doc.colors.itemByName("Paper");
        }
    } catch (e) {
        // Texte Dynamic Access en cas d'erreur
        var logoText = page.textFrames.add();
        logoText.geometricBounds = [12, 15, 28, 75];
        logoText.contents = "Dynamic\\nAccess";
        logoText.paragraphs[0].appliedParagraphStyle = styles.titleStyle;
        logoText.paragraphs[0].justification = Justification.CENTER_ALIGN;
        logoText.paragraphs[0].fillColor = doc.colors.itemByName("Paper");
    }
    
    // Bandeau rouge
    var bandeauFrame = page.rectangles.add();
    bandeauFrame.geometricBounds = [10, 150, 30, 200];
    bandeauFrame.fillColor = doc.colors.itemByName("Rouge");
    
    // Texte du bandeau
    var bandeauText = page.textFrames.add();
    bandeauText.geometricBounds = [12, 155, 28, 195];
    bandeauText.contents = "RENAULT - DACIA - CITROEN - PEUGEOT\\nFiltre à air";
    bandeauText.paragraphs[0].appliedParagraphStyle = styles.bandeauStyle;
    bandeauText.paragraphs[0].justification = Justification.CENTER_ALIGN;
}

// Fonction pour créer un titre de catégorie moderne
function createCategoryTitle(page, categoryName, y, styles) {
    // Titre de la catégorie
    var categoryTitle = page.textFrames.add();
    categoryTitle.geometricBounds = [y, 10, y + 15, 200];
    categoryTitle.contents = categoryName;
    categoryTitle.paragraphs[0].appliedParagraphStyle = styles.categoryStyle;
    categoryTitle.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Ligne décorative moderne sous le titre
    var line = page.graphicLines.add();
    line.strokeColor = doc.colors.itemByName("Rouge");
    line.strokeWeight = 2;
    line.geometricBounds = [y + 16, 20, y + 16, 190];
    
    // Motifs décoratifs modernes
    var motif1 = page.rectangles.add();
    motif1.geometricBounds = [y + 17, 15, y + 19, 17];
    motif1.fillColor = doc.colors.itemByName("Rouge");
    
    var motif2 = page.rectangles.add();
    motif2.geometricBounds = [y + 17, 193, y + 19, 195];
    motif2.fillColor = doc.colors.itemByName("Rouge");
    
    var motif3 = page.rectangles.add();
    motif3.geometricBounds = [y + 17, 102, y + 19, 104];
    motif3.fillColor = doc.colors.itemByName("Orange");
}

// Fonction pour créer un produit individuel
function createProduct(page, product, x, y, width, height, styles) {
    // Cadre du produit
    var productFrame = page.rectangles.add();
    productFrame.geometricBounds = [y, x, y + height, x + width];
    productFrame.strokeColor = doc.colors.itemByName("Black");
    productFrame.strokeWeight = 0.5;
    
    // Référence Dynamic (en haut)
    var refFrame = page.textFrames.add();
    refFrame.geometricBounds = [y + 2, x + 2, y + 12, x + width - 2];
    refFrame.contents = product.Référence;
    refFrame.paragraphs[0].appliedParagraphStyle = styles.refStyle;
    
    // Spécifications (à gauche)
    var specFrame = page.textFrames.add();
    specFrame.geometricBounds = [y + 15, x + 2, y + 45, x + width/2 - 2];
    var specContent = "OEM: " + (product.OEM || '') + "\\n";
    specContent += "SINFA: " + (product.SINFA || '') + "\\n";
    specContent += "MECAFILTRE: " + (product.MECAFILTRE || '') + "\\n";
    specContent += "KNECHT: " + (product.KNECHT || '') + "\\n";
    specContent += "MISFAT: " + (product.MISFAT || '') + "\\n";
    specContent += "MANN: " + (product.MANN || '') + "\\n";
    specContent += "FILTRON: " + (product.FILTRON || '');
    specFrame.contents = specContent;
    specFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    
    // Image du produit (au centre) - ZOOMÉE pour remplir le frame
    if (product.Images) {
        try {
            var imagePath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/" + product.Images;
            var imageFile = new File(imagePath);
            if (imageFile.exists) {
                var imageFrame = page.rectangles.add();
                imageFrame.geometricBounds = [y + 15, x + width/2 + 2, y + 45, x + width - 2];
                imageFrame.place(File(imagePath));
                // ZOOMER l'image pour remplir complètement le frame
                imageFrame.fit(FittingOptions.FILL_FRAME);
                imageFrame.fit(FittingOptions.CENTER_CONTENT);
            }
        } catch (e) {
            // Si l'image n'existe pas, on continue
        }
    }
    
    // Applications (en bas) - Texte français, direction LTR
    var appFrame = page.textFrames.add();
    appFrame.geometricBounds = [y + 50, x + 2, y + height - 2, x + width - 2];
    appFrame.contents = product.Applications || '';
    appFrame.paragraphs[0].appliedParagraphStyle = styles.appStyle;
    
    // Appliquer la direction LTR pour le texte français
    try {
        appFrame.paragraphs[0].justification = Justification.LEFT_ALIGN;
        appFrame.paragraphs[0].paragraphDirection = ParagraphDirection.LEFT_TO_RIGHT;
    } catch (e) {
        // Fallback si la propriété n'est pas supportée
    }
}

// Fonction pour créer une page de produits avec 8 produits (2x4)
function createProductPage(doc, products, pageNumber, styles, categoryName) {
    var page = doc.pages.add();
    
    // Créer l'en-tête
    createPageHeader(page, styles);
    
    // Créer le titre de catégorie moderne
    createCategoryTitle(page, categoryName, 35, styles);
    
    // Dimensions pour 8 produits (2x4)
    var pageWidth = 210; // mm
    var pageHeight = 297; // mm
    var margin = 10; // mm
    var productWidth = (pageWidth - 3 * margin) / 2; // 2 colonnes
    var productHeight = (pageHeight - 4 * margin - 60) / 4; // 4 rangées, moins l'en-tête et le titre
    
    // Position de départ (après l'en-tête et le titre)
    var startY = 60;
    var startX = margin;
    
    // Créer les 8 produits
    for (var i = 0; i < Math.min(8, products.length); i++) {
        var row = Math.floor(i / 2);
        var col = i % 2;
        
        var x = startX + col * (productWidth + margin);
        var y = startY + row * (productHeight + margin);
        
        createProduct(page, products[i], x, y, productWidth, productHeight, styles);
    }
    
    // Numéro de page
    var pageNumFrame = page.textFrames.add();
    pageNumFrame.geometricBounds = [pageHeight - 15, pageWidth/2 - 10, pageHeight - 10, pageWidth/2 + 10];
    pageNumFrame.contents = "-" + pageNumber + "-";
    pageNumFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    pageNumFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Lignes décoratives autour du numéro de page
    var line1 = page.graphicLines.add();
    line1.strokeColor = doc.colors.itemByName("Rouge");
    line1.strokeWeight = 1;
    line1.geometricBounds = [pageHeight - 12, pageWidth/2 - 20, pageHeight - 12, pageWidth/2 - 15];
    
    var line2 = page.graphicLines.add();
    line2.strokeColor = doc.colors.itemByName("Rouge");
    line2.strokeWeight = 1;
    line2.geometricBounds = [pageHeight - 12, pageWidth/2 + 15, pageHeight - 12, pageWidth/2 + 20];
}

// Début du script principal
var doc = createDocument();
var styles = createStyles(doc);

// Données des produits par catégorie
${this.generateScriptContent(categorizedProducts)}

alert("Catalogue Dynamic V4 généré avec succès !");
`;
    }

    generateScriptContent(categorizedProducts) {
        let script = '';
        
        // Fonction pour nettoyer et échapper les chaînes
        const cleanString = (str) => {
            if (!str) return '';
            let cleaned = str.toString();
            cleaned = cleaned.replace(/[\r\n\t\f\v]/g, ' ');
            cleaned = cleaned.replace(/\\/g, '\\\\');
            cleaned = cleaned.replace(/"/g, '\\"');
            cleaned = cleaned.replace(/\s+/g, ' ');
            return cleaned.trim();
        };
        
        // Traiter chaque catégorie
        categorizedProducts.forEach((categoryData, categoryIndex) => {
            const categoryName = categoryData.catégorie;
            const products = categoryData.produits;
            
            script += `\n// Catégorie: ${categoryName}\n`;
            
            // Créer le tableau de produits pour cette catégorie
            script += `var products${categoryIndex} = [\n`;
            
            // Ajouter tous les produits de cette catégorie
            products.forEach(product => {
                script += `  {\n`;
                script += `    Référence: "${cleanString(product.Référence)}",\n`;
                script += `    OEM: "${cleanString(product.OEM)}",\n`;
                script += `    SINFA: "${cleanString(product.SINFA)}",\n`;
                script += `    MECAFILTRE: "${cleanString(product.MECAFILTRE)}",\n`;
                script += `    KNECHT: "${cleanString(product.KNECHT)}",\n`;
                script += `    MISFAT: "${cleanString(product.MISFAT)}",\n`;
                script += `    MANN: "${cleanString(product.MANN)}",\n`;
                script += `    FILTRON: "${cleanString(product.FILTRON)}",\n`;
                script += `    Applications: "${cleanString(product.Applications)}",\n`;
                script += `    Images: "${cleanString(product.Images)}"\n`;
                script += `  },\n`;
            });
            
            script += `];\n`;
            
            // Créer les pages pour cette catégorie (8 produits par page)
            const pagesNeeded = Math.ceil(products.length / 8);
            
            for (let pageNum = 1; pageNum <= pagesNeeded; pageNum++) {
                const startIndex = (pageNum - 1) * 8;
                const endIndex = Math.min(startIndex + 8, products.length);
                const pageProducts = products.slice(startIndex, endIndex);
                
                script += `createProductPage(doc, products${categoryIndex}.slice(${startIndex}, ${endIndex}), ${pageNum}, styles, "${cleanString(categoryName)}");\n`;
            }
            
            script += `\n`;
        });
        
        return script;
    }
}

// Exécution du script
if (require.main === module) {
    const generator = new CatalogueDynamicV4Generator();
    generator.generateCatalogue();
}

module.exports = CatalogueDynamicV4Generator;
