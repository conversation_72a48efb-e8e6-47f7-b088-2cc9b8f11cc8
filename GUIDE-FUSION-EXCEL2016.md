# 🔧 Guide Rapide - Fusion Excel 2016

## ⚡ **Solution rapide pour Excel 2016**

### **Formule simple avec ampersand (&) :**
```
=A2&" | "&A3&" | "&A4&" | "&A5
```

## 📊 **Exemple pratique**

### **Vos données brutes :**
```
Reference | Nom | Description | Equivalent | Image
DFA0011 | Filtre RENAULT - Clio | Clio 1.9 D (64 HP) | RENAULT: 7701034705 | DFA0011.jpg
         |                     | Clio 1.8i RT        | ASZ: ASZ1011      |
         |                     | Clio 1.7 RT         | SINFA: 22877      |
         |                     | Baccara (92 HP)     | MECAFILTRE: EL3413|
```

### **Formule de fusion :**
```
Description: =C2&" | "&C3&" | "&C4&" | "&C5
Equivalent: =D2&" | "&D3&" | "&D4&" | "&D5
```

### **Résultat :**
```
Description: Clio 1.9 D (64 HP) | Clio 1.8i RT | Clio 1.7 RT | Baccara (92 HP)
Equivalent: RENAULT: 7701034705 | ASZ: ASZ1011 | SINFA: 22877 | MECAFILTRE: EL3413
```

## 🚀 **Étapes rapides**

### **1. Dans Excel :**
- **Tapez** : `=C2&" | "&C3&" | "&C4&" | "&C5`
- **Appuyez** sur Entrée
- **Copiez** la formule vers le bas

### **2. Pour d'autres produits :**
- **Ajustez** les références : `=C6&" | "&C7&" | "&C8&" | "&C9`
- **Ou copiez** et Excel ajuste automatiquement

## 💡 **Conseils**

- **Séparateur " | "** : Facile à lire
- **Copier-coller** : Plus rapide que retaper
- **Testez** d'abord avec 2-3 lignes
- **Sauvegardez** avant de faire des modifications

## 🔄 **Workflow**

1. **Créez** votre structure Excel
2. **Ajoutez** la formule de fusion
3. **Testez** que ça fonctionne
4. **Sauvegardez** en `catalogue.xlsx`
5. **Testez** avec `npm run convert-excel-simple`

---

**🎯 Prêt à tester ! Commencez avec une formule simple.**
