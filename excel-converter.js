const XLSX = require('xlsx');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class ExcelConverter {
    constructor() {
        this.sourceExcelPath = path.join(__dirname, 'excel', 'catalogue.xlsx');
        this.convertedExcelPath = path.join(__dirname, 'excel', 'catalogue_converted.xlsx');
        this.outputPath = path.join(__dirname, 'output');
    }

    async convertExcel() {
        try {
            console.log(chalk.blue('🔄 Conversion automatique du fichier Excel...\n'));
            
            // Vérifier que le fichier source existe
            if (!await fs.pathExists(this.sourceExcelPath)) {
                throw new Error('Fichier Excel source non trouvé. Placez votre fichier dans excel/catalogue.xlsx');
            }

            // Lire le fichier Excel source
            const workbook = XLSX.readFile(this.sourceExcelPath);
            console.log(chalk.green(`✅ Fichier Excel lu avec ${workbook.SheetNames.length} onglets`));
            
            // Convertir chaque onglet
            const allProducts = [];
            const categoryMapping = this.getCategoryMapping();
            
            for (const sheetName of workbook.SheetNames) {
                try {
                    console.log(chalk.blue(`📋 Traitement de l'onglet : ${sheetName}`));
                    
                    const products = await this.convertSheet(workbook.Sheets[sheetName], sheetName, categoryMapping);
                    allProducts.push(...products);
                    
                    console.log(chalk.green(`   ✓ ${products.length} produits convertis`));
                } catch (error) {
                    console.log(chalk.yellow(`   ⚠️ Erreur sur l'onglet ${sheetName} : ${error.message}`));
                    console.log(chalk.gray('   L\'onglet sera ignoré, les autres continuent...'));
                    continue;
                }
            }

            // Créer le fichier converti
            await this.createConvertedExcel(allProducts);
            
            console.log(chalk.green('\n🎉 Conversion terminée avec succès !'));
            console.log(chalk.blue(`📊 Total : ${allProducts.length} produits convertis`));
            console.log(chalk.blue(`📁 Fichier créé : excel/catalogue_converted.xlsx`));
            
            return allProducts;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de la conversion :'), error.message);
            throw error;
        }
    }

    getCategoryMapping() {
        // Mapping des onglets vers les catégories du système
        return {
            'F, Air EU': { category: 'Filtre air', subCategory: 'Européenne' },
            'F,Air AS': { category: 'Filtre air', subCategory: 'Asie' },
            'Gasoil EU': { category: 'Gasoil', subCategory: 'Européenne' },
            'Gasoil AS': { category: 'Gasoil', subCategory: 'Asie' },
            'Huile EU': { category: 'Huile', subCategory: 'Européenne' },
            'Huile AS': { category: 'Huile', subCategory: 'Asie' },
            'F, AIR PL': { category: 'Filtre air', subCategory: 'Poids lourds' },
            'HABITACLE': { category: 'Habitacle', subCategory: 'Standard' }
        };
    }

    async convertSheet(worksheet, sheetName, categoryMapping) {
        const products = [];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (data.length <= 1) return products; // Pas de données
        
        const headers = data[0];
        const categoryInfo = categoryMapping[sheetName] || { category: 'Autre', subCategory: 'Général' };
        
        let currentReference = '';
        let currentBrand = '';
        let currentApplications = '';
        let currentCorrespondances = '';
        let currentImage = '';
        
        // Traiter chaque ligne
        for (let i = 1; i < data.length; i++) {
            const row = data[i];
            
            // Détecter les cellules fusionnées et les nouvelles références
            if (row[0] && typeof row[0] === 'string' && row[0].trim) {
                currentBrand = row[0].trim();
            }
            
            if (row[1] && typeof row[1] === 'string' && row[1].trim) {
                currentReference = row[1].trim();
                currentApplications = '';
                currentCorrespondances = '';
                currentImage = '';
            }
            
            // Accumuler les applications et correspondances
            if (row[2] && typeof row[2] === 'string' && row[2].trim) {
                currentApplications += (currentApplications ? ' | ' : '') + row[2].trim();
            }
            
            if (row[3] && typeof row[3] === 'string' && row[3].trim) {
                currentCorrespondances += (currentCorrespondances ? ' | ' : '') + row[3].trim();
            }
            
            if (row[4] && typeof row[4] === 'string' && row[4].trim) {
                currentImage = row[4].trim();
            }
            
            // Si on a une référence complète, créer le produit
            if (currentReference && currentReference !== 'Référence') {
                const product = this.createProduct(
                    currentReference,
                    currentBrand,
                    currentApplications,
                    currentCorrespondances,
                    currentImage,
                    categoryInfo
                );
                
                if (product) {
                    products.push(product);
                }
            }
        }
        
        return products;
    }

    createProduct(reference, brand, applications, correspondances, image, categoryInfo) {
        if (!reference || reference === 'Référence') return null;
        
        // Créer un nom descriptif basé sur la marque et les applications
        const nom = this.generateProductName(brand, applications);
        
        // Créer une description basée sur les applications
        const description = this.generateDescription(applications);
        
        // Extraire les équivalents principaux
        const equivalent = this.extractMainEquivalent(correspondances);
        
        return {
            Reference: reference,
            Nom: nom,
            Categorie: categoryInfo.category,
            SousCategorie: categoryInfo.subCategory,
            Description: description,
            Equivalent: equivalent,
            Image: image || reference + '.jpg',
            // Données originales pour référence
            _original: {
                brand: brand,
                applications: applications,
                correspondances: correspondances
            }
        };
    }

    generateProductName(brand, applications) {
        if (!brand || !applications) return 'Filtre Dynamic Access';
        
        // Extraire le premier modèle d'application pour le nom
        const firstApp = applications.split('|')[0].trim();
        const modelMatch = firstApp.match(/([A-Za-z]+\s+\d+[A-Za-z]*)/);
        
        if (modelMatch) {
            return `Filtre ${brand} - ${modelMatch[1]}`;
        }
        
        return `Filtre ${brand} - ${firstApp.substring(0, 30)}...`;
    }

    generateDescription(applications) {
        if (!applications) return 'Filtre haute performance Dynamic Access';
        
        // Limiter la description à 200 caractères
        const cleanApps = applications.replace(/\|/g, ', ');
        return cleanApps.length > 200 ? cleanApps.substring(0, 197) + '...' : cleanApps;
    }

    extractMainEquivalent(correspondances) {
        if (!correspondances) return 'DA001';
        
        // Extraire le premier équivalent
        const firstEquiv = correspondances.split('|')[0].trim();
        const equivMatch = firstEquiv.match(/([A-Z]+\d+)/);
        
        if (equivMatch) {
            return equivMatch[1];
        }
        
        return 'DA001';
    }

    async createConvertedExcel(products) {
        // Créer le dossier output s'il n'existe pas
        await fs.ensureDir(this.outputPath);
        
        // Créer le workbook converti
        const workbook = XLSX.utils.book_new();
        
        // En-têtes du système
        const headers = ['Reference', 'Nom', 'Categorie', 'SousCategorie', 'Description', 'Equivalent', 'Image'];
        
        // Convertir les produits en lignes
        const rows = [headers];
        products.forEach(product => {
            rows.push([
                product.Reference,
                product.Nom,
                product.Categorie,
                product.SousCategorie,
                product.Description,
                product.Equivalent,
                product.Image
            ]);
        });
        
        // Créer la feuille
        const worksheet = XLSX.utils.aoa_to_sheet(rows);
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Catalogue_Converti');
        
        // Sauvegarder
        XLSX.writeFile(workbook, this.convertedExcelPath);
        
        // Créer aussi un fichier de configuration pour le catalogue
        await this.createCatalogueConfig(products);
    }

    async createCatalogueConfig(products) {
        const config = {
            metadata: {
                title: "Catalogue Dynamic Access - Converti",
                version: "1.0",
                convertedAt: new Date().toISOString(),
                totalProducts: products.length,
                sourceSheets: Object.keys(this.getCategoryMapping())
            },
            layout: {
                productsPerPage: 8,
                pageSize: "A4",
                margins: { top: 20, bottom: 20, left: 20, right: 20 }
            },
            products: products,
            categories: this.organizeByCategories(products)
        };
        
        const configPath = path.join(this.outputPath, 'catalogue_config.json');
        await fs.writeJson(configPath, config, { spaces: 2 });
        
        console.log(chalk.blue('📝 Fichier de configuration créé : output/catalogue_config.json'));
    }

    organizeByCategories(products) {
        const categories = {};
        
        products.forEach(product => {
            if (!categories[product.Categorie]) {
                categories[product.Categorie] = {};
            }
            if (!categories[product.Categorie][product.SousCategorie]) {
                categories[product.Categorie][product.SousCategorie] = [];
            }
            
            categories[product.Categorie][product.SousCategorie].push(product);
        });
        
        return categories;
    }
}

// Exécution du script
if (require.main === module) {
    const converter = new ExcelConverter();
    converter.convertExcel()
        .then(() => {
            console.log(chalk.blue('\n🚀 Prochaines étapes :'));
            console.log(chalk.gray('1. Vérifiez le fichier converti : excel/catalogue_converted.xlsx'));
            console.log(chalk.gray('2. Générez le catalogue : npm run generate'));
            console.log(chalk.gray('3. Ou utilisez : npm run list-products pour voir les produits'));
        })
        .catch(error => {
            console.error(chalk.red('\n❌ Conversion échouée'));
            process.exit(1);
        });
}

module.exports = ExcelConverter;
