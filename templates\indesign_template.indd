# Template InDesign - Catalogue Dynamic Access

Ce fichier est un exemple de structure pour votre template InDesign.

## Structure recommandée :

### Page 1 : Couverture
- Logo Dynamic Access
- Titre "Catalogue Filtres"
- Sous-titre "Qualité et Performance"
- Date de publication

### Pages 2-3 : Table des matières
- Liste des catégories
- Numéros de pages
- Navigation claire

### Pages suivantes : Produits par catégorie
- En-tête de catégorie (ex: "Filtres à Air")
- Sous-catégorie (ex: "Européenne")
- Grille de 8 produits par page
- Chaque produit contient :
  * Image
  * Référence
  * Nom
  * Description
  * Référence équivalente

## Styles recommandés :
- Titre principal : Aria<PERSON>, 18pt
- Sous-titres : Arial, 14pt
- Noms de produits : Arial Bold, 12pt
- Références : Arial, 10pt
- Descriptions : Arial, 10pt

## Mise en page :
- Format A4
- Marges : 20mm
- Grille : 2 colonnes x 4 lignes = 8 produits par page
- Espacement entre produits : 10mm

## Utilisation :
1. Créez votre template dans InDesign
2. Appliquez les styles recommandés
3. Sauvegardez dans le dossier templates/
4. Le script utilisera ce template comme base

Note : Ce fichier .indd doit être créé manuellement dans InDesign car c'est un format binaire.
