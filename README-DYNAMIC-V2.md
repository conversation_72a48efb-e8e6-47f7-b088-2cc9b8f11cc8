# Catalogue Dynamic Access V2

## 🎯 Système de Génération de Catalogue Moderne

Ce projet génère automatiquement un catalogue InDesign professionnel avec le design **Dynamic Access V2** à partir de votre fichier Excel existant, avec support arabe complet et design moderne.

## ✨ Nouvelles Fonctionnalités V2

### 🏷️ Titres de Catégorie Automatiques
- **Nom de la feuille Excel** utilisé comme titre de catégorie
- **Design moderne** avec couleur gris foncé (80%)
- **Motifs décoratifs** sous le titre (lignes rouges, carrés colorés)

### 🖼️ Logo Intelligent
- **Utilise logo.png** s'il existe dans le dossier `images/`
- **Fallback automatique** vers "Dynamic Access" si l'image n'existe pas
- **Aucun prix** affiché dans le logo

### 🌍 Support Arabe Complet
- **Direction RTL** (Right-to-Left) pour le texte arabe
- **Caractères arabes** supportés nativement
- **Applications véhicules** en arabe avec alignement automatique

### 🎨 Design Moderne
- **Titres de catégorie** avec style gris foncé moderne
- **Motifs décoratifs** modernes et professionnels
- **Layout optimisé** pour l'impression

## 📁 Structure du Projet

```
Dynamic-Access/
├── excel/
│   └── catalogue.xlsx                    # Votre fichier Excel avec les produits
├── images/                               # Dossier pour les images des produits
│   └── logo.png                         # Logo Dynamic (optionnel)
├── output/
│   └── generate_catalogue_dynamic_v2.jsx # Script InDesign V2 généré
├── generate-catalogue-dynamic-v2.js      # Générateur V2 principal
├── lance-dynamic-v2.js                  # Lanceur V2 simple
└── GUIDE-DYNAMIC-V2.md                  # Guide d'utilisation V2
```

## 🚀 Utilisation Rapide

1. **Préparer vos données** :
   - Placez votre fichier Excel dans `excel/catalogue.xlsx`
   - Placez vos images dans le dossier `images/`
   - Placez `logo.png` dans `images/` (optionnel)

2. **Générer le catalogue** :
   ```bash
   node lance-dynamic-v2.js
   ```

3. **Créer dans InDesign** :
   - Ouvrir Adobe InDesign
   - Fichier > Scripts > Exécuter le script
   - Sélectionner : `output/generate_catalogue_dynamic_v2.jsx`

## 📊 Format Excel Requis

| Colonne A | Colonne B | Colonne C | Colonne D |
|-----------|-----------|-----------|-----------|
| Référence | Applications | Correspondances | Images |
| DFA0011 | Renault Clio | RENAULT:123, SINFA:456 | DFA0011.jpg |

## 🎨 Design Appliqué V2

- ✅ **Logo Dynamic Access** (logo.png ou texte)
- ✅ **Titre de catégorie** avec nom de la feuille Excel
- ✅ **Design moderne** gris foncé avec motifs décoratifs
- ✅ **Support arabe** avec direction RTL automatique
- ✅ **En-tête gris** avec logo Dynamic
- ✅ **Bandeau rouge** avec texte bilingue
- ✅ **6 produits par page** en grille 2x3
- ✅ **Références en orange**
- ✅ **Spécifications extraites** automatiquement
- ✅ **Images intégrées**
- ✅ **Numérotation professionnelle**

## 🔧 Extraction Intelligente

Le système extrait automatiquement ces références de la colonne "Correspondances" :
- **OEM** (RENAULT:)
- **SINFA** (SINFA:)
- **MECAFILTRE** (MECAFILTRE:)
- **KNECHT** (KNECHT:)
- **MISFAT** (MISFAT:)
- **MANN** (MANN:)
- **FILTRON** (FILTRON:)

## 📋 Fichiers Principaux V2

- **`generate-catalogue-dynamic-v2.js`** : Générateur V2 avec toutes les nouvelles fonctionnalités
- **`lance-dynamic-v2.js`** : Lanceur V2 simple et rapide
- **`GUIDE-DYNAMIC-V2.md`** : Guide détaillé d'utilisation V2

## 🌟 Avantages de la V2

### ✅ Multi-Feuilles Excel
- **Lecture automatique** de toutes les feuilles Excel
- **Titre de catégorie** basé sur le nom de la feuille
- **Organisation intelligente** des produits par catégorie

### ✅ Logo Intelligent
- **Utilise logo.png** s'il existe
- **Fallback automatique** vers le texte
- **Aucun prix** affiché

### ✅ Support Arabe Complet
- **Direction RTL** automatique
- **Caractères arabes** natifs
- **Alignement automatique** pour l'arabe

### ✅ Design Moderne
- **Titres de catégorie** avec style gris foncé
- **Motifs décoratifs** modernes
- **Layout professionnel** optimisé

## 🎉 Résultat Final

Un catalogue professionnel moderne avec le design **Dynamic Access V2** :
- En-tête avec logo intelligent
- Titres de catégorie basés sur vos feuilles Excel
- Support arabe complet avec RTL
- Design moderne et professionnel
- Prêt pour l'impression

---

## 📖 Documentation Complète

Consultez `GUIDE-DYNAMIC-V2.md` pour des instructions détaillées et le dépannage.

## 🚀 Mise à Jour depuis V1

Si vous utilisez déjà la V1, la V2 apporte :
- ✅ Support multi-feuilles Excel
- ✅ Titres de catégorie automatiques
- ✅ Logo intelligent sans prix
- ✅ Support arabe complet
- ✅ Design moderne amélioré
- ✅ Meilleure organisation des produits
