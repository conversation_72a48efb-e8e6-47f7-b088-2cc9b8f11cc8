# Guide Catalogue Dynamic V3 - Version Corrigée

## 🎯 Objectif
G<PERSON>rer automatiquement un catalogue InDesign avec le design Dynamic Access V3 corrigé à partir de votre fichier Excel existant, avec images zoomées et texte français.

## ✨ Corrections Appliquées V3

### 🖼️ Images Zoomées
- **Images des produits** : ZOOMÉES pour remplir complètement le frame (pas d'espace blanc)
- **Logo** : ZOOMÉ pour remplir complètement le frame
- **Utilise `FittingOptions.FILL_FRAME`** au lieu de `FILL_FRAME_PROPORTIONALLY`

### 🏷️ Titre de Catégorie Correct
- **Nom de la feuille Excel** utilisé comme titre de catégorie
- **Exemple** : "Filtre à Air Européennes" (votre feuille Excel)
- **Design moderne** avec couleur gris foncé (80%)

### 🇫🇷 Texte Français par Défaut
- **Supprimé le support arabe** par défaut
- **Direction LTR** (Left-to-Right) pour le français
- **Texte du bandeau** : "Filtre à air" (au lieu du texte arabe)
- **Applications véhicules** en français avec alignement LTR

### 🎨 Design Moderne
- **Titres de catégorie** avec style gris foncé moderne
- **Motifs décoratifs** modernes (lignes rouges, carrés colorés)
- **Layout optimisé** pour l'impression professionnelle

## 📋 Prérequis
- Node.js installé
- Adobe InDesign
- Votre fichier Excel `excel/catalogue.xlsx`
- Logo `images/logo.png` (optionnel)

## 📁 Structure Excel Requise
Votre fichier `excel/catalogue.xlsx` doit contenir ces colonnes :
- **Colonne A** : Référence (ex: DFA0011)
- **Colonne B** : Applications véhicules
- **Colonne C** : Correspondances (format: RENAULT:123, SINFA:456, MECAFILTRE:789)
- **Colonne D** : Nom du fichier image (ex: DFA0011.jpg)

## 🚀 Utilisation

### 1. Préparer les données
- Placez votre fichier Excel dans `excel/catalogue.xlsx`
- Placez vos images dans le dossier `images/`
- Placez `logo.png` dans `images/` (optionnel)

### 2. Générer le catalogue
```bash
node lance-dynamic-v3.js
```

### 3. Créer le catalogue dans InDesign
1. Ouvrir Adobe InDesign
2. **Fichier** > **Scripts** > **Exécuter le script**
3. Sélectionner `output/generate_catalogue_dynamic_v3.jsx`
4. Le catalogue sera créé automatiquement

## 🎨 Design Appliqué V3

### En-tête de Page
- **Logo Dynamic** : Zone gris foncé avec logo.png zoomé ou texte "Dynamic Access"
- **Bandeau rouge** : "RENAULT - DACIA - CITROEN - PEUGEOT" + "Filtre à air"

### Titres de Catégorie
- **Nom de la feuille Excel** : "Filtre à Air Européennes"
- **Couleur** : Gris foncé (80%)
- **Style** : Bold, 18pt
- **Motifs** : Ligne rouge + carrés décoratifs

### Layout des Produits
- **6 produits par page** en grille 2x3
- **Référence Dynamic** : En orange, taille 14pt, Bold
- **Spécifications** : Extraites automatiquement des correspondances
- **Images** : Intégrées et ZOOMÉES pour remplir le frame
- **Applications** : Liste des véhicules compatibles en français (LTR)

### Extraction Automatique
Le système extrait automatiquement de vos correspondances :
- **OEM** : Référence RENAULT
- **SINFA** : Référence SINFA
- **MECAFILTRE** : Référence MECAFILTRE
- **KNECHT** : Référence KNECHT
- **MISFAT** : Référence MISFAT
- **MANN** : Référence MANN
- **FILTRON** : Référence FILTRON

## 📁 Fichiers Utilisés

```
Dynamic-Access/
├── excel/catalogue.xlsx                    # Votre fichier Excel
├── images/                                 # Vos images des filtres
│   └── logo.png                           # Logo Dynamic (optionnel)
├── generate-catalogue-dynamic-v3.js        # Générateur V3 corrigé
├── lance-dynamic-v3.js                    # Lanceur V3
└── output/
    └── generate_catalogue_dynamic_v3.jsx  # Script InDesign V3 corrigé
```

## 🔧 Préparation des Images

1. **Images produits** : Placer dans `images/` avec les noms de votre Excel
2. **Logo** : Placer `logo.png` dans `images/` (optionnel)
3. **Format recommandé** : JPG ou PNG
4. **Résolution** : minimum 300 DPI pour l'impression
5. **Images seront automatiquement zoomées** pour remplir le frame

## 📊 Exemple de Données

Votre Excel ressemble à ceci :
```
Référence    | Applications                    | Correspondances                                    | Images
DFA0011      | Renault Clio 1.9 D (64 HP)...  | RENAULT: 7701034705 | ASZ: ASZ1011 | SINFA: 22877... | DFA0011.jpg
```

Le système extrait automatiquement :
- **Référence** : DFA0011
- **OEM** : 7701034705
- **SINFA** : 22877
- **Applications** : Renault Clio 1.9 D (64 HP)...
- **Images** : DFA0011.jpg
- **Catégorie** : Nom de la feuille Excel

## 🎯 Résultat Final V3

Le catalogue généré aura :
- ✅ En-tête avec logo Dynamic Access zoomé
- ✅ Titre de catégorie avec le nom de votre feuille Excel
- ✅ 6 filtres par page avec toutes leurs spécifications
- ✅ Images des produits ZOOMÉES (pas d'espace blanc)
- ✅ Applications véhicules en français (LTR)
- ✅ Numérotation des pages avec éléments décoratifs
- ✅ Design moderne et professionnel
- ✅ Prêt pour l'impression

## 🐛 Dépannage

### Erreur "Fichier Excel non trouvé"
- Vérifier que `excel/catalogue.xlsx` existe
- Vérifier les permissions du fichier

### Erreur "Images non trouvées"
- Vérifier que les images sont dans `images/`
- Vérifier que les noms correspondent à la colonne "Images"

### Problème d'extraction des correspondances
- Vérifier le format des correspondances
- Utiliser des séparateurs : `|` ou `,` ou `;`
- Format attendu : `RENAULT: 123 | SINFA: 456 | MANN: 789`

### Problème avec les images zoomées
- Vérifier que les images sont bien dans le dossier `images/`
- Le système utilise `FILL_FRAME` pour zoomer automatiquement
- Les images seront recadrées si nécessaire pour remplir le frame

## 📞 Support

Pour toute question :
1. Vérifier ce guide
2. Consulter les logs d'erreur
3. Vérifier la structure de votre Excel
4. Tester avec quelques produits d'abord
5. Vérifier que le logo.png est bien placé

---

## 🔄 Différences avec V2

**V3 corrige V2 :**
- ✅ Images des produits ZOOMÉES (pas d'espace blanc)
- ✅ Logo ZOOMÉ (pas d'espace blanc)
- ✅ Titre de catégorie correct (nom de la feuille Excel)
- ✅ Texte français par défaut (pas d'arabe)
- ✅ Direction LTR pour le français
- ✅ Bandeau avec "Filtre à air" en français

---

**🎉 Votre Excel est maintenant prêt à générer un catalogue parfait avec le design Dynamic Access V3 !**
