const XLSX = require('xlsx');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class CatalogueDynamicGenerator {
    constructor() {
        this.excelPath = path.join(__dirname, 'excel', 'catalogue.xlsx');
        this.outputPath = path.join(__dirname, 'output');
        this.imagesPath = path.join(__dirname, 'images');
    }

    async generateCatalogue() {
        try {
            console.log(chalk.blue('🚀 Début de la génération du catalogue Dynamic...'));
            
            // Vérifier que le fichier Excel existe
            if (!await fs.pathExists(this.excelPath)) {
                throw new Error('Fichier Excel non trouvé. Veuillez créer le fichier excel/catalogue.xlsx');
            }

            // Lire le fichier Excel
            const products = await this.readExcelFile(this.excelPath);
            
            // Organiser par catégories
            const organizedProducts = this.organizeByCategories(products);
            
            // Générer le fichier InDesign
            await this.generateInDesignFile(organizedProducts);
            
            console.log(chalk.green('🎉 Catalogue Dynamic généré avec succès !'));
            console.log(chalk.blue('📁 Fichier créé dans le dossier output/'));
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de la génération :'), error.message);
        }
    }

    async readExcelFile(filePath) {
        try {
            const workbook = XLSX.readFile(filePath);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            
            // Convertir en JSON avec gestion des en-têtes
            const data = XLSX.utils.sheet_to_json(worksheet, { 
                header: 1,
                defval: '' 
            });
            
            if (data.length < 2) {
                throw new Error('Le fichier Excel doit contenir au moins un en-tête et une ligne de données');
            }
            
            const headers = data[0];
            const products = [];
            
            // Traiter chaque ligne de données
            for (let i = 1; i < data.length; i++) {
                const row = data[i];
                if (row.length === 0 || !row[0]) continue; // Ignorer les lignes vides
                
                // Extraire les données selon la structure existante
                const reference = this.normalizeString(row[0] || ''); // Référence
                const applications = this.normalizeString(row[1] || ''); // Applications
                const correspondances = this.normalizeString(row[2] || ''); // Correspondances
                const images = this.normalizeString(row[3] || ''); // Images
                
                // Parser les correspondances pour extraire les différentes références
                const parsedCorrespondances = this.parseCorrespondances(correspondances);
                
                const product = {
                    Référence: reference,
                    OEM: parsedCorrespondances.OEM || '',
                    SINFA: parsedCorrespondances.SINFA || '',
                    MECAFILTRE: parsedCorrespondances.MECAFILTRE || '',
                    KNECHT: parsedCorrespondances.KNECHT || '',
                    MISFAT: parsedCorrespondances.MISFAT || '',
                    MANN: parsedCorrespondances.MANN || '',
                    FILTRON: parsedCorrespondances.FILTRON || '',
                    Applications: applications,
                    Images: images
                };
                
                // Vérifier que la référence n'est pas vide
                if (product.Référence.trim()) {
                    products.push(product);
                }
            }
            
            console.log(`✅ ${products.length} produits chargés depuis Excel`);
            return products;
            
        } catch (error) {
            console.error('❌ Erreur lors de la lecture du fichier Excel:', error.message);
            throw error;
        }
    }
    
    // Fonction pour parser les correspondances
    parseCorrespondances(correspondances) {
        const result = {};
        
        if (!correspondances) return result;
        
        // Diviser par les séparateurs possibles
        const parts = correspondances.split(/[|,;]/);
        
        parts.forEach(part => {
            const trimmed = part.trim();
            if (!trimmed) return;
            
            // Chercher les patterns de correspondance
            if (trimmed.includes('RENAULT:')) {
                result.OEM = trimmed.replace('RENAULT:', '').trim();
            } else if (trimmed.includes('ASZ:')) {
                // ASZ est déjà dans la référence principale
            } else if (trimmed.includes('SINFA:')) {
                result.SINFA = trimmed.replace('SINFA:', '').trim();
            } else if (trimmed.includes('MECAFILTRE:')) {
                result.MECAFILTRE = trimmed.replace('MECAFILTRE:', '').trim();
            } else if (trimmed.includes('KNECHT:')) {
                result.KNECHT = trimmed.replace('KNECHT:', '').trim();
            } else if (trimmed.includes('MISFAT:')) {
                result.MISFAT = trimmed.replace('MISFAT:', '').trim();
            } else if (trimmed.includes('MANN:')) {
                result.MANN = trimmed.replace('MANN:', '').trim();
            } else if (trimmed.includes('FILTRON:')) {
                result.FILTRON = trimmed.replace('FILTRON:', '').trim();
            }
        });
        
        return result;
    }
    
    // Fonction pour normaliser les chaînes de caractères
    normalizeString(str) {
        if (!str) return '';
        
        // Convertir en chaîne
        let normalized = str.toString();
        
        // Normaliser les caractères arabes
        normalized = normalized.replace(/[٠-٩]/g, (d) => '٠١٢٣٤٥٦٧٨٩'.indexOf(d));
        normalized = normalized.replace(/[۰-۹]/g, (d) => '۰۱۲۳۴۵۶۷۸۹'.indexOf(d));
        
        // Remplacer les caractères arabes spécifiques
        normalized = normalized.replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2').replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5').replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9');
        normalized = normalized.replace(/۰/g, '0').replace(/۱/g, '1').replace(/۲/g, '2').replace(/۳/g, '3').replace(/۴/g, '4').replace(/۵/g, '5').replace(/۶/g, '6').replace(/۷/g, '7').replace(/۸/g, '8').replace(/۹/g, '9');
        
        // Nettoyer les caractères spéciaux
        normalized = normalized.replace(/[\r\n\t\f\v]/g, ' ');
        normalized = normalized.replace(/\s+/g, ' ');
        normalized = normalized.trim();
        
        return normalized;
    }

    organizeByCategories(products) {
        const categories = {};
        
        products.forEach(product => {
            // Utiliser la référence comme catégorie (ex: ASZ1008 -> ASZ1000)
            const category = product.Référence.substring(0, 6) + '00';
            
            if (!categories[category]) {
                categories[category] = [];
            }
            categories[category].push(product);
        });
        
        return categories;
    }

    async generateInDesignFile(organizedProducts) {
        const scriptContent = this.createInDesignScript(organizedProducts);
        const outputFile = path.join(this.outputPath, 'generate_catalogue_dynamic.jsx');
        
        await fs.ensureDir(this.outputPath);
        await fs.writeFile(outputFile, scriptContent, 'utf8');
        
        console.log(`📄 Script InDesign généré: ${outputFile}`);
    }

    createInDesignScript(organizedProducts) {
        return `// Script InDesign pour Catalogue Dynamic
// Généré automatiquement pour votre structure Excel existante

// Fonction pour créer un nouveau document
function createDocument() {
    var doc = app.documents.add();
    doc.documentPreferences.pageWidth = "210mm";
    doc.documentPreferences.pageHeight = "297mm";
    doc.documentPreferences.facingPages = false;
    doc.documentPreferences.pageOrientation = PageOrientation.PORTRAIT;
    
    // Créer les couleurs nécessaires
    createColors(doc);
    
    return doc;
}

// Fonction pour créer les couleurs
function createColors(doc) {
    // Couleur rouge pour les bandeaux
    try {
        var redColor = doc.colors.itemByName("Rouge");
        if (!redColor || redColor.isValid === false) {
            redColor = doc.colors.add();
            redColor.name = "Rouge";
            redColor.colorValue = [0, 100, 100, 0]; // Rouge
        }
    } catch (e) {
        redColor = doc.colors.add();
        redColor.name = "Rouge";
        redColor.colorValue = [0, 100, 100, 0];
    }
    
    // Couleur orange pour les références
    try {
        var orangeColor = doc.colors.itemByName("Orange");
        if (!orangeColor || orangeColor.isValid === false) {
            orangeColor = doc.colors.add();
            orangeColor.name = "Orange";
            orangeColor.colorValue = [0, 50, 100, 0]; // Orange
        }
    } catch (e) {
        orangeColor = doc.colors.add();
        orangeColor.name = "Orange";
        orangeColor.colorValue = [0, 50, 100, 0];
    }
    
    // Couleur grise pour le logo
    try {
        var grayColor = doc.colors.itemByName("Gris");
        if (!grayColor || grayColor.isValid === false) {
            grayColor = doc.colors.add();
            grayColor.name = "Gris";
            grayColor.colorValue = [0, 0, 0, 50]; // Gris 50%
        }
    } catch (e) {
        grayColor = doc.colors.add();
        grayColor.name = "Gris";
        grayColor.colorValue = [0, 0, 0, 50];
    }
}

// Fonction pour créer les styles
function createStyles(doc) {
    // Style pour le titre principal
    var titleStyle = doc.paragraphStyles.add();
    titleStyle.name = "TitrePrincipal";
    titleStyle.appliedFont = app.fonts.itemByName("Arial");
    titleStyle.fontStyle = "Bold";
    titleStyle.pointSize = 24; // Utiliser pointSize au lieu de fontSize
    titleStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les références Dynamic
    var refStyle = doc.paragraphStyles.add();
    refStyle.name = "ReferenceDynamic";
    refStyle.appliedFont = app.fonts.itemByName("Arial");
    refStyle.fontStyle = "Bold";
    refStyle.pointSize = 14; // Utiliser pointSize au lieu de fontSize
    refStyle.fillColor = doc.colors.itemByName("Orange");
    
    // Style pour les spécifications
    var specStyle = doc.paragraphStyles.add();
    specStyle.name = "Specifications";
    specStyle.appliedFont = app.fonts.itemByName("Arial");
    specStyle.pointSize = 8; // Utiliser pointSize au lieu de fontSize
    specStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les applications
    var appStyle = doc.paragraphStyles.add();
    appStyle.name = "Applications";
    appStyle.appliedFont = app.fonts.itemByName("Arial");
    appStyle.pointSize = 7; // Utiliser pointSize au lieu de fontSize
    appStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour le bandeau rouge
    var bandeauStyle = doc.paragraphStyles.add();
    bandeauStyle.name = "BandeauRouge";
    bandeauStyle.appliedFont = app.fonts.itemByName("Arial");
    bandeauStyle.fontStyle = "Bold";
    bandeauStyle.pointSize = 12; // Utiliser pointSize au lieu de fontSize
    bandeauStyle.fillColor = doc.colors.itemByName("Paper");
    
    return {
        titleStyle: titleStyle,
        refStyle: refStyle,
        specStyle: specStyle,
        appStyle: appStyle,
        bandeauStyle: bandeauStyle
    };
}

// Fonction pour créer l'en-tête de page
function createPageHeader(page, styles) {
    // Logo Dynamic (zone grise)
    var logoFrame = page.rectangles.add();
    logoFrame.geometricBounds = [10, 10, 30, 80];
    logoFrame.fillColor = doc.colors.itemByName("Gris");
    
    // Texte Dynamic Access
    var logoText = page.textFrames.add();
    logoText.geometricBounds = [12, 15, 28, 75];
    logoText.contents = "Dynamic\\nAccess";
    logoText.paragraphs[0].appliedParagraphStyle = styles.titleStyle;
    logoText.paragraphs[0].justification = Justification.CENTER_ALIGN;
    logoText.paragraphs[0].fillColor = doc.colors.itemByName("Paper");
    
    // Bandeau rouge
    var bandeauFrame = page.rectangles.add();
    bandeauFrame.geometricBounds = [10, 150, 30, 200];
    bandeauFrame.fillColor = doc.colors.itemByName("Rouge");
    
    // Texte du bandeau
    var bandeauText = page.textFrames.add();
    bandeauText.geometricBounds = [12, 155, 28, 195];
    bandeauText.contents = "RENAULT - DACIA - CITROEN - PEUGEOT\\nمصفاة هواء / Filtre à air";
    bandeauText.paragraphs[0].appliedParagraphStyle = styles.bandeauStyle;
    bandeauText.paragraphs[0].justification = Justification.CENTER_ALIGN;
}

// Fonction pour créer un produit individuel
function createProduct(page, product, x, y, width, height, styles) {
    // Cadre du produit
    var productFrame = page.rectangles.add();
    productFrame.geometricBounds = [y, x, y + height, x + width];
    productFrame.strokeColor = doc.colors.itemByName("Black");
    productFrame.strokeWeight = 0.5;
    
    // Référence Dynamic (en haut)
    var refFrame = page.textFrames.add();
    refFrame.geometricBounds = [y + 2, x + 2, y + 12, x + width - 2];
    refFrame.contents = product.Référence;
    refFrame.paragraphs[0].appliedParagraphStyle = styles.refStyle;
    
    // Spécifications (à gauche)
    var specFrame = page.textFrames.add();
    specFrame.geometricBounds = [y + 15, x + 2, y + 45, x + width/2 - 2];
    var specContent = "OEM: " + (product.OEM || '') + "\\n";
    specContent += "SINFA: " + (product.SINFA || '') + "\\n";
    specContent += "MECAFILTRE: " + (product.MECAFILTRE || '') + "\\n";
    specContent += "KNECHT: " + (product.KNECHT || '') + "\\n";
    specContent += "MISFAT: " + (product.MISFAT || '') + "\\n";
    specContent += "MANN: " + (product.MANN || '') + "\\n";
    specContent += "FILTRON: " + (product.FILTRON || '');
    specFrame.contents = specContent;
    specFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    
    // Image du produit (au centre)
    if (product.Images) {
        try {
            var imagePath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/" + product.Images;
            var imageFile = new File(imagePath);
            if (imageFile.exists) {
                var imageFrame = page.rectangles.add();
                imageFrame.geometricBounds = [y + 15, x + width/2 + 2, y + 45, x + width - 2];
                imageFrame.place(File(imagePath));
                imageFrame.fit(FittingOptions.FILL_FRAME_PROPORTIONALLY);
                imageFrame.fit(FittingOptions.CENTER_CONTENT);
            }
        } catch (e) {
            // Si l'image n'existe pas, on continue
        }
    }
    
    // Applications (en bas)
    var appFrame = page.textFrames.add();
    appFrame.geometricBounds = [y + 50, x + 2, y + height - 2, x + width - 2];
    appFrame.contents = product.Applications || '';
    appFrame.paragraphs[0].appliedParagraphStyle = styles.appStyle;
}

// Fonction pour créer une page de produits
function createProductPage(doc, products, pageNumber, styles) {
    var page = doc.pages.add();
    
    // Créer l'en-tête
    createPageHeader(page, styles);
    
    // Dimensions pour 6 produits (2x3)
    var pageWidth = 210; // mm
    var pageHeight = 297; // mm
    var margin = 10; // mm
    var productWidth = (pageWidth - 3 * margin) / 2; // 2 colonnes
    var productHeight = (pageHeight - 4 * margin - 30) / 3; // 3 rangées, moins l'en-tête
    
    // Position de départ (après l'en-tête)
    var startY = 40;
    var startX = margin;
    
    // Créer les 6 produits
    for (var i = 0; i < Math.min(6, products.length); i++) {
        var row = Math.floor(i / 2);
        var col = i % 2;
        
        var x = startX + col * (productWidth + margin);
        var y = startY + row * (productHeight + margin);
        
        createProduct(page, products[i], x, y, productWidth, productHeight, styles);
    }
    
    // Numéro de page
    var pageNumFrame = page.textFrames.add();
    pageNumFrame.geometricBounds = [pageHeight - 15, pageWidth/2 - 10, pageHeight - 10, pageWidth/2 + 10];
    pageNumFrame.contents = "-" + pageNumber + "-";
    pageNumFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    pageNumFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Lignes décoratives autour du numéro de page
    var line1 = page.graphicLines.add();
    line1.strokeColor = doc.colors.itemByName("Rouge");
    line1.strokeWeight = 1;
    line1.geometricBounds = [pageHeight - 12, pageWidth/2 - 20, pageHeight - 12, pageWidth/2 - 15];
    
    var line2 = page.graphicLines.add();
    line2.strokeColor = doc.colors.itemByName("Rouge");
    line2.strokeWeight = 1;
    line2.geometricBounds = [pageHeight - 12, pageWidth/2 + 15, pageHeight - 12, pageWidth/2 + 20];
}

// Début du script principal
var doc = createDocument();
var styles = createStyles(doc);

// Données des produits
${this.generateScriptContent(organizedProducts)}

alert("Catalogue Dynamic généré avec succès !");
`;
    }

    generateScriptContent(organizedProducts) {
        let script = '';
        
        // Fonction pour nettoyer et échapper les chaînes
        const cleanString = (str) => {
            if (!str) return '';
            let cleaned = str.toString();
            cleaned = cleaned.replace(/[\r\n\t\f\v]/g, ' ');
            cleaned = cleaned.replace(/\\/g, '\\\\');
            cleaned = cleaned.replace(/"/g, '\\"');
            cleaned = cleaned.replace(/\s+/g, ' ');
            return cleaned.trim();
        };
        
        // Traiter chaque catégorie
        Object.keys(organizedProducts).forEach(category => {
            script += `\n// Catégorie: ${category}\n`;
            
            // Créer le tableau de produits pour cette catégorie
            script += `var products${category.replace(/[^a-zA-Z0-9]/g, '')} = [\n`;
            
            // Ajouter tous les produits de cette catégorie
            organizedProducts[category].forEach(product => {
                script += `  {\n`;
                script += `    Référence: "${cleanString(product.Référence)}",\n`;
                script += `    OEM: "${cleanString(product.OEM)}",\n`;
                script += `    SINFA: "${cleanString(product.SINFA)}",\n`;
                script += `    MECAFILTRE: "${cleanString(product.MECAFILTRE)}",\n`;
                script += `    KNECHT: "${cleanString(product.KNECHT)}",\n`;
                script += `    MISFAT: "${cleanString(product.MISFAT)}",\n`;
                script += `    MANN: "${cleanString(product.MANN)}",\n`;
                script += `    FILTRON: "${cleanString(product.FILTRON)}",\n`;
                script += `    Applications: "${cleanString(product.Applications)}",\n`;
                script += `    Images: "${cleanString(product.Images)}"\n`;
                script += `  },\n`;
            });
            
            script += `];\n`;
            
            // Créer les pages pour cette catégorie
            const products = organizedProducts[category];
            const pagesNeeded = Math.ceil(products.length / 6);
            
            for (let pageNum = 1; pageNum <= pagesNeeded; pageNum++) {
                const startIndex = (pageNum - 1) * 6;
                const endIndex = Math.min(startIndex + 6, products.length);
                const pageProducts = products.slice(startIndex, endIndex);
                
                script += `createProductPage(doc, products${category.replace(/[^a-zA-Z0-9]/g, '')}.slice(${startIndex}, ${endIndex}), ${pageNum}, styles);\n`;
            }
            
            script += `\n`;
        });
        
        return script;
    }
}

// Exécution du script
if (require.main === module) {
    const generator = new CatalogueDynamicGenerator();
    generator.generateCatalogue();
}

module.exports = CatalogueDynamicGenerator;
