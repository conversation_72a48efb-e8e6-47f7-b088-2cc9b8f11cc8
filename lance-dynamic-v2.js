#!/usr/bin/env node

const path = require('path');
const fs = require('fs-extra');
const CatalogueDynamicV2Generator = require('./generate-catalogue-dynamic-v2.js');

console.log('🎯 Générateur Catalogue Dynamic V2');
console.log('==================================\n');

async function main() {
    try {
        // Vérifier que le fichier Excel existe
        const excelPath = path.join(__dirname, 'excel', 'catalogue.xlsx');

        if (!await fs.pathExists(excelPath)) {
            console.log('❌ Fichier excel/catalogue.xlsx non trouvé');
            console.log('💡 Veuillez placer votre fichier Excel dans le dossier excel/');
            process.exit(1);
        }

        console.log('✅ Fichier Excel trouvé : excel/catalogue.xlsx');

        // Vérifier le dossier images
        const imagesPath = path.join(__dirname, 'images');
        if (!await fs.pathExists(imagesPath)) {
            console.log('📁 Création du dossier images/');
            await fs.ensureDir(imagesPath);
        }

        // Vérifier si logo.png existe
        const logoPath = path.join(imagesPath, 'logo.png');
        if (!await fs.pathExists(logoPath)) {
            console.log('⚠️  Logo logo.png non trouvé dans images/');
            console.log('💡 Le système utilisera le texte "Dynamic Access" à la place');
        } else {
            console.log('✅ Logo logo.png trouvé');
        }

        console.log('\n🚀 Génération du catalogue Dynamic V2...');

        // Créer le générateur et lancer la génération
        const generator = new CatalogueDynamicV2Generator();
        await generator.generateCatalogue();

        console.log('\n✅ Catalogue Dynamic V2 généré avec succès !');
        console.log('\n📋 Prochaines étapes :');
        console.log('1. Ouvrir Adobe InDesign');
        console.log('2. Aller dans Fichier > Scripts > Exécuter le script');
        console.log('3. Sélectionner : output/generate_catalogue_dynamic_v2.jsx');
        console.log('4. Le catalogue sera créé automatiquement avec le design Dynamic V2');

        console.log('\n📁 Fichier créé :');
        console.log('- output/generate_catalogue_dynamic_v2.jsx');

        console.log('\n🎨 Nouvelles fonctionnalités V2 :');
        console.log('- ✅ Nom de la feuille Excel comme titre de catégorie');
        console.log('- ✅ Logo sans prix (utilise logo.png ou texte)');
        console.log('- ✅ Design moderne pour les titres de catégorie');
        console.log('- ✅ Support arabe avec direction RTL');
        console.log('- ✅ Caractères arabes supportés');
        console.log('- ✅ Motifs décoratifs modernes');
        console.log('- ✅ En-tête avec logo Dynamic Access');
        console.log('- ✅ 6 produits par page en grille 2x3');
        console.log('- ✅ Références en orange');
        console.log('- ✅ Spécifications extraites automatiquement');
        console.log('- ✅ Images intégrées');
        console.log('- ✅ Applications véhicules');
        console.log('- ✅ Numérotation des pages');

    } catch (error) {
        console.error('\n❌ Erreur :', error.message);
        console.log('\n🔧 Solutions possibles :');
        console.log('- Vérifiez que votre fichier Excel est bien formaté');
        console.log('- Vérifiez que les colonnes sont : Référence, Applications, Correspondances, Images');
        console.log('- Vérifiez les permissions des dossiers');
        console.log('- Vérifiez que le fichier Excel contient au moins une feuille');
    }
}

// Lancer le script
main();
