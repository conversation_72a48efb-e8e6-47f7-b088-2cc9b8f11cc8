const XLSX = require('xlsx');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class SimpleExcelConverter {
    constructor() {
        this.sourceExcelPath = path.join(__dirname, 'excel', 'catalogue.xlsx');
        this.convertedExcelPath = path.join(__dirname, 'excel', 'catalogue_converted.xlsx');
        this.outputPath = path.join(__dirname, 'output');
    }

    async convertExcel() {
        try {
            console.log(chalk.blue('🔄 Conversion simplifiée du fichier Excel...\n'));
            
            // Vérifier que le fichier source existe
            if (!await fs.pathExists(this.sourceExcelPath)) {
                throw new Error('Fichier Excel source non trouvé. Placez votre fichier dans excel/catalogue.xlsx');
            }

            // Lire le fichier Excel source
            const workbook = XLSX.readFile(this.sourceExcelPath);
            console.log(chalk.green(`✅ Fichier Excel lu avec ${workbook.SheetNames.length} feuilles`));
            
            // Convertir chaque feuille
            const allProducts = [];
            
            for (const sheetName of workbook.SheetNames) {
                try {
                    console.log(chalk.blue(`📋 Traitement de la feuille : ${sheetName}`));
                    
                    const products = await this.convertSheet(workbook.Sheets[sheetName], sheetName);
                    allProducts.push(...products);
                    
                    console.log(chalk.green(`   ✓ ${products.length} produits convertis`));
                } catch (error) {
                    console.log(chalk.yellow(`   ⚠️ Erreur sur la feuille ${sheetName} : ${error.message}`));
                    console.log(chalk.gray('   La feuille sera ignorée, les autres continuent...'));
                    continue;
                }
            }

            // Créer le fichier converti
            await this.createConvertedExcel(allProducts);
            
            console.log(chalk.green('\n🎉 Conversion terminée avec succès !'));
            console.log(chalk.blue(`📊 Total : ${allProducts.length} produits convertis`));
            console.log(chalk.blue(`📁 Fichier créé : excel/catalogue_converted.xlsx`));
            
            return allProducts;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de la conversion :'), error.message);
            throw error;
        }
    }

    async convertSheet(worksheet, sheetName) {
        const products = [];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (data.length <= 1) return products; // Pas de données
        
        const headers = data[0];
        
        // Vérifier que les colonnes sont correctes
        if (!this.validateHeaders(headers)) {
            throw new Error(`Structure de colonnes incorrecte. Attendu: Reference, Nom, Description, Equivalent, Image`);
        }
        
        // Traiter chaque ligne de produit
        for (let i = 1; i < data.length; i++) {
            const row = data[i];
            
            // Vérifier que la ligne a des données
            if (this.isValidProductRow(row)) {
                const product = this.createProduct(row, sheetName);
                if (product) {
                    products.push(product);
                }
            }
        }
        
        return products;
    }

    validateHeaders(headers) {
        const expectedHeaders = ['Reference', 'Nom', 'Description', 'Equivalent', 'Image'];
        return expectedHeaders.every(header => headers.includes(header));
    }

    isValidProductRow(row) {
        // Vérifier que la référence existe et n'est pas vide
        return row[0] && typeof row[0] === 'string' && row[0].trim() && row[0] !== 'Reference';
    }

    createProduct(row, sheetName) {
        const [reference, nom, description, equivalent, image] = row;
        
        if (!reference || !nom) return null;
        
        return {
            Reference: reference.trim(),
            Nom: nom.trim(),
            Categorie: sheetName, // Utilise le nom de la feuille comme catégorie
            Description: description ? description.trim() : '',
            Equivalent: equivalent ? equivalent.trim() : '',
            Image: image ? image.trim() : reference.trim() + '.jpg'
        };
    }

    async createConvertedExcel(products) {
        // Créer le dossier output s'il n'existe pas
        await fs.ensureDir(this.outputPath);
        
        // Créer le workbook converti
        const workbook = XLSX.utils.book_new();
        
        // En-têtes du système
        const headers = ['Reference', 'Nom', 'Categorie', 'Description', 'Equivalent', 'Image'];
        
        // Convertir les produits en lignes
        const rows = [headers];
        products.forEach(product => {
            rows.push([
                product.Reference,
                product.Nom,
                product.Categorie,
                product.Description,
                product.Equivalent,
                product.Image
            ]);
        });
        
        // Créer la feuille
        const worksheet = XLSX.utils.aoa_to_sheet(rows);
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Catalogue_Converti');
        
        // Sauvegarder
        XLSX.writeFile(workbook, this.convertedExcelPath);
        
        // Créer aussi un fichier de configuration pour le catalogue
        await this.createCatalogueConfig(products);
    }

    async createCatalogueConfig(products) {
        const config = {
            metadata: {
                title: "Catalogue Dynamic Access - Simplifié",
                version: "2.0",
                convertedAt: new Date().toISOString(),
                totalProducts: products.length,
                sourceSheets: [...new Set(products.map(p => p.Categorie))]
            },
            layout: {
                productsPerPage: 8,
                pageSize: "A4",
                margins: { top: 20, bottom: 20, left: 20, right: 20 }
            },
            products: products,
            categories: this.organizeByCategories(products)
        };
        
        const configPath = path.join(this.outputPath, 'catalogue_config.json');
        await fs.writeJson(configPath, config, { spaces: 2 });
        
        console.log(chalk.blue('📝 Fichier de configuration créé : output/catalogue_config.json'));
    }

    organizeByCategories(products) {
        const categories = {};
        
        products.forEach(product => {
            if (!categories[product.Categorie]) {
                categories[product.Categorie] = [];
            }
            categories[product.Categorie].push(product);
        });
        
        return categories;
    }
}

// Exécution du script
if (require.main === module) {
    const converter = new SimpleExcelConverter();
    converter.convertExcel()
        .then(() => {
            console.log(chalk.blue('\n🚀 Prochaines étapes :'));
            console.log(chalk.gray('1. Vérifiez le fichier converti : excel/catalogue_converted.xlsx'));
            console.log(chalk.gray('2. Générez le catalogue : npm run generate'));
            console.log(chalk.gray('3. Ou utilisez : npm run list-products pour voir les produits'));
        })
        .catch(error => {
            console.error(chalk.red('\n❌ Conversion échouée'));
            process.exit(1);
        });
}

module.exports = SimpleExcelConverter;
