const XLSX = require('xlsx');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class CatalogueGenerator {
    constructor() {
        this.excelPath = path.join(__dirname, 'excel', 'catalogue.xlsx');
        this.outputPath = path.join(__dirname, 'output');
        this.imagesPath = path.join(__dirname, 'images');
        this.templatesPath = path.join(__dirname, 'templates');
    }

    async generateCatalogue() {
        try {
            console.log(chalk.blue('🚀 Début de la génération du catalogue...'));
            
            // Vérifier que le fichier Excel existe
            if (!await fs.pathExists(this.excelPath)) {
                throw new Error('Fichier Excel non trouvé. Veuillez créer le fichier excel/catalogue.xlsx');
            }

            // Lire le fichier Excel
            const products = await this.readExcelFile(this.excelPath);
            
            // Organiser par catégories
            const organizedProducts = this.organizeByCategories(products);
            
            // Générer le fichier InDesign
            await this.generateInDesignFile(organizedProducts);
            
            console.log(chalk.green('🎉 Catalogue généré avec succès !'));
            console.log(chalk.blue('📁 Fichier créé dans le dossier output/'));
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de la génération :'), error.message);
        }
    }

    async readExcelFile(filePath) {
        try {
            const workbook = XLSX.readFile(filePath);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            
            // Convertir en JSON avec gestion des en-têtes
            const data = XLSX.utils.sheet_to_json(worksheet, { 
                header: 1,
                defval: '' // Valeur par défaut pour les cellules vides
            });
            
            if (data.length < 2) {
                throw new Error('Le fichier Excel doit contenir au moins un en-tête et une ligne de données');
            }
            
            const headers = data[0];
            const products = [];
            
            // Traiter chaque ligne de données
            for (let i = 1; i < data.length; i++) {
                const row = data[i];
                if (row.length === 0 || !row[0]) continue; // Ignorer les lignes vides
                
                const product = {
                    Référence: this.normalizeString(row[0] || ''),
                    Applications: this.normalizeString(row[1] || ''),
                    Correspondances: this.normalizeString(row[2] || ''),
                    Images: this.normalizeString(row[3] || '')
                };
                
                // Vérifier que la référence n'est pas vide
                if (product.Référence.trim()) {
                    products.push(product);
                }
            }
            
            console.log(`✅ ${products.length} produits chargés depuis Excel`);
            return products;
            
        } catch (error) {
            console.error('❌ Erreur lors de la lecture du fichier Excel:', error.message);
            throw error;
        }
    }
    
    // Fonction pour normaliser les chaînes de caractères
    normalizeString(str) {
        if (!str) return '';
        
        // Convertir en chaîne
        let normalized = str.toString();
        
        // Normaliser les caractères arabes
        normalized = normalized.replace(/[٠-٩]/g, (d) => '٠١٢٣٤٥٦٧٨٩'.indexOf(d));
        normalized = normalized.replace(/[۰-۹]/g, (d) => '۰۱۲۳۴۵۶۷۸۹'.indexOf(d));
        
        // Remplacer les caractères arabes spécifiques
        normalized = normalized.replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2').replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5').replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9');
        normalized = normalized.replace(/۰/g, '0').replace(/۱/g, '1').replace(/۲/g, '2').replace(/۳/g, '3').replace(/۴/g, '4').replace(/۵/g, '5').replace(/۶/g, '6').replace(/۷/g, '7').replace(/۸/g, '8').replace(/۹/g, '9');
        
        // Nettoyer les retours à la ligne et tabulations
        normalized = normalized.replace(/[\r\n\t\f\v]/g, ' ');
        
        // Supprimer les espaces multiples
        normalized = normalized.replace(/\s+/g, ' ');
        
        // Supprimer les espaces en début et fin
        normalized = normalized.trim();
        
        return normalized;
    }

    organizeByCategories(products) {
        const categories = {};
        
        // Lire le nom du classeur Excel
        const workbook = XLSX.readFile(this.excelPath);
        const sheetName = workbook.SheetNames[0];
        
        products.forEach(product => {
            // Utiliser le nom du classeur Excel comme catégorie
            const category = sheetName;
            
            if (!categories[category]) {
                categories[category] = [];
            }
            
            // Ajouter le produit en respectant l'ordre original
            categories[category].push(product);
        });
        
        return categories;
    }

    async generateInDesignFile(organizedProducts) {
        // Créer le dossier output s'il n'existe pas
        await fs.ensureDir(this.outputPath);
        
        // Générer le fichier InDesign (.indd) ou un fichier de configuration
        const indesignConfig = this.createInDesignConfig(organizedProducts);
        
        const outputFile = path.join(this.outputPath, 'catalogue_config.json');
        await fs.writeJson(outputFile, indesignConfig, { spaces: 2 });
        
        // Créer aussi un fichier de script InDesign
        const scriptContent = this.generateInDesignScript(organizedProducts);
        const scriptFile = path.join(this.outputPath, 'generate_catalogue.jsx');
        await fs.writeFile(scriptFile, scriptContent);
        
        console.log(chalk.blue('📝 Fichiers de configuration créés :'));
        console.log(chalk.gray('   - catalogue_config.json (configuration)'));
        console.log(chalk.gray('   - generate_catalogue.jsx (script InDesign)'));
    }

    createInDesignConfig(organizedProducts) {
        return {
            metadata: {
                title: "Catalogue Dynamic Access",
                version: "1.0",
                generatedAt: new Date().toISOString(),
                totalProducts: this.countTotalProducts(organizedProducts)
            },
            layout: {
                productsPerPage: 8,
                pageSize: "A4",
                margins: { top: 20, bottom: 20, left: 20, right: 20 }
            },
            categories: organizedProducts,
            styles: {
                title: { font: "Arial Bold", size: 18, color: "#2C3E50" },
                subtitle: { font: "Arial", size: 14, color: "#34495E" },
                productName: { font: "Arial Bold", size: 12, color: "#2C3E50" },
                productRef: { font: "Arial", size: 10, color: "#7F8C8D" },
                description: { font: "Arial", size: 10, color: "#2C3E50" }
            }
        };
    }

    generateInDesignScript(organizedProducts) {
        return `// Script InDesign pour générer le catalogue Dynamic Access
// Ce script doit être exécuté dans InDesign

// Fonction pour créer un document s'il n'existe pas
function createDocument() {
    var doc = app.activeDocument;
    if (!doc) {
        doc = app.documents.add();
        doc.documentPreferences.pageWidth = "210mm";
        doc.documentPreferences.pageHeight = "297mm";
        doc.documentPreferences.facingPages = false;
    }
    return doc;
}

// Fonction pour créer des styles de paragraphe
function createStyles(doc) {
    var titleStyle, subtitleStyle, productStyle, headerStyle, refStyle, categoryStyle;
    
    // Créer le style Titre
    try {
        titleStyle = doc.paragraphStyles.itemByName("Titre");
        if (!titleStyle || titleStyle.isValid === false) {
            titleStyle = doc.paragraphStyles.add();
            titleStyle.name = "Titre";
            titleStyle.appliedFont = app.fonts.itemByName("Arial");
            titleStyle.fontStyle = "Bold";
            titleStyle.fontSize = 18;
            titleStyle.fillColor = doc.colors.itemByName("Black");
        }
    } catch (e) {
        try {
            titleStyle = doc.paragraphStyles.itemByName("Titre");
        } catch (e2) {
            titleStyle = doc.paragraphStyles.add();
            titleStyle.name = "Titre";
            titleStyle.appliedFont = app.fonts.itemByName("Arial");
            titleStyle.fontStyle = "Bold";
            titleStyle.fontSize = 18;
            titleStyle.fillColor = doc.colors.itemByName("Black");
        }
    }
    
    // Créer le style Sous-titre
    try {
        subtitleStyle = doc.paragraphStyles.itemByName("Sous-titre");
        if (!subtitleStyle || subtitleStyle.isValid === false) {
            subtitleStyle = doc.paragraphStyles.add();
            subtitleStyle.name = "Sous-titre";
            subtitleStyle.appliedFont = app.fonts.itemByName("Arial");
            subtitleStyle.fontSize = 14;
            subtitleStyle.fillColor = doc.colors.itemByName("Black");
        }
    } catch (e) {
        try {
            subtitleStyle = doc.paragraphStyles.itemByName("Sous-titre");
        } catch (e2) {
            subtitleStyle = doc.paragraphStyles.add();
            subtitleStyle.name = "Sous-titre";
            subtitleStyle.appliedFont = app.fonts.itemByName("Arial");
            subtitleStyle.fontSize = 14;
            subtitleStyle.fillColor = doc.colors.itemByName("Black");
        }
    }
    
    // Créer le style Produit
    try {
        productStyle = doc.paragraphStyles.itemByName("Produit");
        if (!productStyle || productStyle.isValid === false) {
            productStyle = doc.paragraphStyles.add();
            productStyle.name = "Produit";
            productStyle.appliedFont = app.fonts.itemByName("Arial");
            productStyle.fontSize = 9;
            productStyle.fillColor = doc.colors.itemByName("Black");
        }
    } catch (e) {
        try {
            productStyle = doc.paragraphStyles.itemByName("Produit");
        } catch (e2) {
            productStyle = doc.paragraphStyles.add();
            productStyle.name = "Produit";
            productStyle.appliedFont = app.fonts.itemByName("Arial");
            productStyle.fontSize = 9;
            productStyle.fillColor = doc.colors.itemByName("Black");
        }
    }
    
    // Créer le style En-tête
    try {
        headerStyle = doc.paragraphStyles.itemByName("En-tête");
        if (!headerStyle || headerStyle.isValid === false) {
            headerStyle = doc.paragraphStyles.add();
            headerStyle.name = "En-tête";
            headerStyle.appliedFont = app.fonts.itemByName("Arial");
            headerStyle.fontStyle = "Bold";
            headerStyle.fontSize = 12;
            headerStyle.fillColor = doc.colors.itemByName("Black");
        }
    } catch (e) {
        try {
            headerStyle = doc.paragraphStyles.itemByName("En-tête");
        } catch (e2) {
            headerStyle = doc.paragraphStyles.add();
            headerStyle.name = "En-tête";
            headerStyle.appliedFont = app.fonts.itemByName("Arial");
            headerStyle.fontStyle = "Bold";
            headerStyle.fontSize = 12;
            headerStyle.fillColor = doc.colors.itemByName("Black");
        }
    }
    
    // Créer le style Référence (orange)
    try {
        refStyle = doc.paragraphStyles.itemByName("Référence");
        if (!refStyle || refStyle.isValid === false) {
            refStyle = doc.paragraphStyles.add();
            refStyle.name = "Référence";
            refStyle.appliedFont = app.fonts.itemByName("Arial");
            refStyle.fontStyle = "Bold";
            refStyle.fontSize = 12;
            // Créer une couleur orange si elle n'existe pas
            try {
                var orangeColor = doc.colors.itemByName("Orange");
                if (!orangeColor || orangeColor.isValid === false) {
                    orangeColor = doc.colors.add();
                    orangeColor.name = "Orange";
                    orangeColor.colorValue = [0, 50, 100, 0]; // Orange
                }
                refStyle.fillColor = orangeColor;
            } catch (e) {
                refStyle.fillColor = doc.colors.itemByName("Black");
            }
        }
    } catch (e) {
        try {
            refStyle = doc.paragraphStyles.itemByName("Référence");
        } catch (e2) {
            refStyle = doc.paragraphStyles.add();
            refStyle.name = "Référence";
            refStyle.appliedFont = app.fonts.itemByName("Arial");
            refStyle.fontStyle = "Bold";
            refStyle.fontSize = 12;
            try {
                var orangeColor = doc.colors.itemByName("Orange");
                if (!orangeColor || orangeColor.isValid === false) {
                    orangeColor = doc.colors.add();
                    orangeColor.name = "Orange";
                    orangeColor.colorValue = [0, 50, 100, 0]; // Orange
                }
                refStyle.fillColor = orangeColor;
            } catch (e) {
                refStyle.fillColor = doc.colors.itemByName("Black");
            }
        }
    }
    
    // Créer le style Catégorie (orange)
    try {
        categoryStyle = doc.paragraphStyles.itemByName("Catégorie");
        if (!categoryStyle || categoryStyle.isValid === false) {
            categoryStyle = doc.paragraphStyles.add();
            categoryStyle.name = "Catégorie";
            categoryStyle.appliedFont = app.fonts.itemByName("Arial");
            categoryStyle.fontStyle = "Bold";
            categoryStyle.fontSize = 14;
            // Utiliser la couleur orange
            try {
                var orangeColor = doc.colors.itemByName("Orange");
                if (!orangeColor || orangeColor.isValid === false) {
                    orangeColor = doc.colors.add();
                    orangeColor.name = "Orange";
                    orangeColor.colorValue = [0, 50, 100, 0]; // Orange
                }
                categoryStyle.fillColor = orangeColor;
            } catch (e) {
                categoryStyle.fillColor = doc.colors.itemByName("Black");
            }
        }
    } catch (e) {
        try {
            categoryStyle = doc.paragraphStyles.itemByName("Catégorie");
        } catch (e2) {
            categoryStyle = doc.paragraphStyles.add();
            categoryStyle.name = "Catégorie";
            categoryStyle.appliedFont = app.fonts.itemByName("Arial");
            categoryStyle.fontStyle = "Bold";
            categoryStyle.fontSize = 14;
            try {
                var orangeColor = doc.colors.itemByName("Orange");
                if (!orangeColor || orangeColor.isValid === false) {
                    orangeColor = doc.colors.add();
                    orangeColor.name = "Orange";
                    orangeColor.colorValue = [0, 50, 100, 0]; // Orange
                }
                categoryStyle.fillColor = orangeColor;
            } catch (e) {
                categoryStyle.fillColor = doc.colors.itemByName("Black");
            }
        }
    }
    
    return { 
        titleStyle: titleStyle, 
        subtitleStyle: subtitleStyle, 
        productStyle: productStyle,
        headerStyle: headerStyle,
        refStyle: refStyle,
        categoryStyle: categoryStyle
    };
}

// Fonction pour créer la page de couverture
function createCoverPage(doc, styles) {
    var coverPage = doc.pages[0];
    
    // Titre principal
    var titleFrame = coverPage.textFrames.add();
    titleFrame.geometricBounds = [100, 20, 150, 190];
    titleFrame.contents = "CATALOGUE DYNAMIC ACCESS";
    titleFrame.paragraphs[0].appliedParagraphStyle = styles.titleStyle;
    titleFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Sous-titre
    var subtitleFrame = coverPage.textFrames.add();
    subtitleFrame.geometricBounds = [160, 20, 180, 190];
    subtitleFrame.contents = "Filtres de Qualité et Performance";
    subtitleFrame.paragraphs[0].appliedParagraphStyle = styles.subtitleStyle;
    subtitleFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Date
    var dateFrame = coverPage.textFrames.add();
    dateFrame.geometricBounds = [250, 20, 270, 190];
    dateFrame.contents = "2024";
    dateFrame.paragraphs[0].appliedParagraphStyle = styles.subtitleStyle;
    dateFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
}

// Fonction pour générer les pages de produits avec 6 produits par page (design moderne)
function generateProductPages(products, categoryName) {
    var doc = app.activeDocument;
    var styles = createStyles(doc);
    
    if (!styles.titleStyle || !styles.productStyle || !styles.headerStyle || !styles.refStyle || !styles.categoryStyle) {
        alert("Erreur: Styles non valides");
        return;
    }
    
    var pageWidth = doc.documentPreferences.pageWidth;
    var pageHeight = doc.documentPreferences.pageHeight;
    var productsPerPage = 6; // 6 produits par page pour un meilleur design
    var currentPage = null;
    
    // Créer la première page pour la catégorie
    currentPage = doc.pages.add();
    
    // En-tête de page avec logo (gauche) et catégorie (droite)
    // Insérer le vrai logo (sans texte)
    try {
        var logoPath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/logo.png";
        var logoFile = new File(logoPath);
        if (logoFile.exists) {
            var logoFrame = currentPage.rectangles.add();
            logoFrame.geometricBounds = [20, 20, 50, 100];
            logoFrame.place(File(logoPath));
            logoFrame.fit(FittingOptions.PROPORTIONALLY);
        }
    } catch (e) {
        // Pas de fallback texte, juste ignorer si le logo n'existe pas
    }
    
    var categoryFrame = currentPage.textFrames.add();
    categoryFrame.geometricBounds = [20, pageWidth - 150, 40, pageWidth - 20];
    categoryFrame.contents = categoryName;
    categoryFrame.paragraphs[0].appliedParagraphStyle = styles.categoryStyle;
    categoryFrame.paragraphs[0].justification = Justification.RIGHT_ALIGN;
    
    // Configuration de la grille pour 6 produits (3x2) - design moderne
    var gridStartY = 70;
    var gridStartX = 20;
    var productWidth = 60;
    var productHeight = 120;
    var marginX = 10;
    var marginY = 20;
    
    for (var i = 0; i < products.length; i++) {
        var product = products[i];
        
        // Vérifier si on doit créer une nouvelle page
        if (i > 0 && i % productsPerPage === 0) {
            currentPage = doc.pages.add();
            
            // En-tête de la nouvelle page avec logo
            try {
                var newLogoPath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/logo.png";
                var newLogoFile = new File(newLogoPath);
                if (newLogoFile.exists) {
                    var newLogoFrame = currentPage.rectangles.add();
                    newLogoFrame.geometricBounds = [20, 20, 50, 100];
                    newLogoFrame.place(File(newLogoPath));
                    newLogoFrame.fit(FittingOptions.PROPORTIONALLY);
                }
            } catch (e) {
                // Pas de fallback texte
            }
            
            var newCategoryFrame = currentPage.textFrames.add();
            newCategoryFrame.geometricBounds = [20, pageWidth - 150, 40, pageWidth - 20];
            newCategoryFrame.contents = categoryName;
            newCategoryFrame.paragraphs[0].appliedParagraphStyle = styles.categoryStyle;
            newCategoryFrame.paragraphs[0].justification = Justification.RIGHT_ALIGN;
        }
        
        // Calculer la position dans la grille (3x2)
        var pageIndex = i % productsPerPage;
        var row = Math.floor(pageIndex / 3);
        var col = pageIndex % 3;
        
        var x = gridStartX + col * (productWidth + marginX);
        var y = gridStartY + row * (productHeight + marginY);
        
        // Créer le frame de la référence (en haut)
        var refFrame = currentPage.textFrames.add();
        refFrame.geometricBounds = [y, x, y + 15, x + productWidth];
        refFrame.contents = product.Référence;
        refFrame.paragraphs[0].appliedParagraphStyle = styles.refStyle;
        
        // Créer le frame des applications (à gauche de l'image)
        var appFrame = currentPage.textFrames.add();
        appFrame.geometricBounds = [y + 20, x, y + 80, x + productWidth/2 - 2];
        var appContent = "";
        if (product.Applications) {
            appContent = "Applications:\\n" + product.Applications;
        }
        appFrame.contents = appContent;
        appFrame.paragraphs[0].appliedParagraphStyle = styles.productStyle;
        
        // Créer le frame des correspondances (à droite de l'image)
        var corrFrame = currentPage.textFrames.add();
        corrFrame.geometricBounds = [y + 20, x + productWidth/2 + 2, y + 80, x + productWidth];
        var corrContent = "";
        
        // Traitement sécurisé des correspondances
        if (product.Correspondances && typeof product.Correspondances === 'string' && product.Correspondances.trim() !== '') {
            try {
                // Diviser par | et nettoyer chaque partie
                var parts = product.Correspondances.split('|');
                var cleanParts = [];
                for (var i = 0; i < parts.length; i++) {
                    var part = parts[i].trim();
                    if (part.length > 0) {
                        cleanParts.push(part);
                    }
                }
                
                if (cleanParts.length > 0) {
                    corrContent = "Correspondances:\\n" + cleanParts.join('\\n');
                } else {
                    corrContent = "Correspondances:\\nAucune correspondance";
                }
            } catch (e) {
                corrContent = "Correspondances:\\nAucune correspondance";
            }
        } else if (product.Correspondances) {
            // Si ce n'est pas une chaîne, convertir en chaîne
            corrContent = "Correspondances:\\n" + product.Correspondances.toString();
        } else {
            corrContent = "Correspondances:\\nAucune correspondance";
        }
        
        corrFrame.contents = corrContent;
        corrFrame.paragraphs[0].appliedParagraphStyle = styles.productStyle;
        
        // Créer le frame de l'image (en bas) - optimisé pour s'adapter au cadre
        if (product.Images) {
            try {
                var imagePath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/" + product.Images;
                var imageFile = new File(imagePath);
                if (imageFile.exists) {
                    var imageFrame = currentPage.rectangles.add();
                    imageFrame.geometricBounds = [y + 85, x + 5, y + 115, x + productWidth - 5];
                    imageFrame.place(File(imagePath));
                    // Utiliser FILL_FRAME_PROPORTIONALLY pour s'adapter au cadre sans zoom
                    imageFrame.fit(FittingOptions.FILL_FRAME_PROPORTIONALLY);
                    // Centrer l'image dans le cadre
                    imageFrame.fit(FittingOptions.CENTER_CONTENT);
                }
            } catch (e) {
                // Si l'image n'existe pas, on continue sans erreur
            }
        }
    }
}

// Début du script principal
var doc = createDocument();
var styles = createStyles(doc);

// Créer la page de couverture
createCoverPage(doc, styles);

// Données des produits organisées par catégories
${this.generateScriptContent(organizedProducts)}

alert("Catalogue Dynamic Access généré avec succès !");
`;
    }

    generateScriptContent(organizedProducts) {
        let script = '';
        
        // Fonction pour nettoyer et échapper les chaînes de manière robuste
        const cleanString = (str) => {
            if (!str) return '';
            
            // Convertir en chaîne si ce n'est pas déjà une chaîne
            let cleaned = str.toString();
            
            // Normaliser les caractères arabes vers latins
            cleaned = cleaned.replace(/[٠-٩]/g, (d) => '٠١٢٣٤٥٦٧٨٩'.indexOf(d));
            cleaned = cleaned.replace(/[۰-۹]/g, (d) => '۰۱۲۳۴۵۶۷۸۹'.indexOf(d));
            
            // Remplacer les caractères arabes spécifiques
            cleaned = cleaned.replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2').replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5').replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9');
            cleaned = cleaned.replace(/۰/g, '0').replace(/۱/g, '1').replace(/۲/g, '2').replace(/۳/g, '3').replace(/۴/g, '4').replace(/۵/g, '5').replace(/۶/g, '6').replace(/۷/g, '7').replace(/۸/g, '8').replace(/۹/g, '9');
            
            // Remplacer TOUS les caractères de retour à la ligne et tabulations par des espaces
            cleaned = cleaned.replace(/[\r\n\t\f\v]/g, ' ');
            
            // Remplacer les | par des espaces (pas de retours à la ligne dans les chaînes JS)
            cleaned = cleaned.replace(/\|/g, ' ');
            
            // Échapper les backslashes
            cleaned = cleaned.replace(/\\/g, '\\\\');
            
            // Échapper les guillemets doubles
            cleaned = cleaned.replace(/"/g, '\\"');
            
            // Supprimer les espaces multiples
            cleaned = cleaned.replace(/\s+/g, ' ');
            
            // Supprimer les espaces en début et fin
            cleaned = cleaned.trim();
            
            return cleaned;
        };
        
        // Fonction pour traiter les correspondances de manière sécurisée
        const processCorrespondances = (corr) => {
            if (!corr || typeof corr !== 'string') return 'Aucune correspondance';
            
            // Nettoyer la chaîne
            let cleaned = corr.toString().trim();
            if (!cleaned) return 'Aucune correspondance';
            
            // Diviser par | et nettoyer chaque partie
            const parts = cleaned.split('|').map(part => part.trim()).filter(part => part.length > 0);
            
            if (parts.length === 0) return 'Aucune correspondance';
            
            // Joindre avec des retours à la ligne pour InDesign
            return parts.join('\\n');
        };
        
        // Traiter chaque catégorie (nom de la feuille Excel)
        Object.keys(organizedProducts).forEach(category => {
            script += `\n// Catégorie: ${category}\n`;
            
            // Créer le tableau de produits pour cette catégorie
            script += `var products${category.replace(/[^a-zA-Z0-9]/g, '')} = [\n`;
            
            // Ajouter tous les produits de cette catégorie
            organizedProducts[category].forEach(product => {
                script += `  {\n`;
                script += `    Référence: "${cleanString(product.Référence)}",\n`;
                script += `    Applications: "${cleanString(product.Applications)}",\n`;
                script += `    Correspondances: "${processCorrespondances(product.Correspondances)}",\n`;
                script += `    Images: "${cleanString(product.Images)}"\n`;
                script += `  },\n`;
            });
            
            script += `];\n`;
            script += `generateProductPages(products${category.replace(/[^a-zA-Z0-9]/g, '')}, "${category}");\n\n`;
        });
        
        return script;
    }

    countTotalProducts(organizedProducts) {
        let count = 0;
        Object.values(organizedProducts).forEach(category => {
            Object.values(category).forEach(subCategory => {
                count += subCategory.length;
            });
        });
        return count;
    }
}

// Exécution du script
if (require.main === module) {
    const generator = new CatalogueGenerator();
    generator.generateCatalogue();
}

module.exports = CatalogueGenerator;