# Catalogue Dynamic Access V4 - Version Finale

## 🎯 Système de Génération de Catalogue Parfait

Ce projet génère automatiquement un catalogue InDesign professionnel avec le design **Dynamic Access V4** final à partir de votre fichier Excel existant, avec **8 produits par page**, images zoomées et texte français.

## ✨ Corrections Finales V4

### 📊 Layout 8 Produits par Page
- **8 produits par page** en grille 2x4 (comme votre image)
- **Dimensions optimisées** pour 8 produits
- **Calcul automatique** des pages nécessaires

### 🖼️ Images Zoomées Parfaitement
- **Images des produits** : ZOOMÉES pour remplir complètement le frame (pas d'espace blanc)
- **Logo** : ZOOMÉ pour remplir complètement le frame
- **Utilise `FittingOptions.FILL_FRAME`** pour un zoom parfait

### 🏷️ Titre de Catégorie Correct
- **Nom de la feuille Excel** utilisé comme titre de catégorie
- **Exemple** : "Filtre à Air Européennes" (votre feuille Excel)
- **Design moderne** avec couleur gris foncé (80%)

### 🇫🇷 Texte Français Partout
- **Supprimé le support arabe** par défaut
- **Direction LTR** (Left-to-Right) pour le français
- **Texte du bandeau** : "Filtre à air" (au lieu du texte arabe)
- **Applications véhicules** en français avec alignement LTR
- **Tous les styles** en français

### 🎨 Design Moderne
- **Titres de catégorie** avec style gris foncé moderne
- **Motifs décoratifs** modernes (lignes rouges, carrés colorés)
- **Layout optimisé** pour l'impression

## 📁 Structure du Projet

```
Dynamic-Access/
├── excel/
│   └── catalogue.xlsx                        # Votre fichier Excel avec les produits
├── images/                                   # Dossier pour les images des produits
│   └── logo.png                             # Logo Dynamic (optionnel)
├── output/
│   └── generate_catalogue_dynamic_v4.jsx    # Script InDesign V4 final
├── generate-catalogue-dynamic-v4.js          # Générateur V4 final
├── lance-dynamic-v4.js                      # Lanceur V4
└── GUIDE-DYNAMIC-V4.md                      # Guide d'utilisation V4
```

## 🚀 Utilisation Rapide

1. **Préparer vos données** :
   - Placez votre fichier Excel dans `excel/catalogue.xlsx`
   - Placez vos images dans le dossier `images/`
   - Placez `logo.png` dans `images/` (optionnel)

2. **Générer le catalogue** :
   ```bash
   node lance-dynamic-v4.js
   ```

3. **Créer dans InDesign** :
   - Ouvrir Adobe InDesign
   - Fichier > Scripts > Exécuter le script
   - Sélectionner : `output/generate_catalogue_dynamic_v4.jsx`

## 📊 Format Excel Requis

| Colonne A | Colonne B | Colonne C | Colonne D |
|-----------|-----------|-----------|-----------|
| Référence | Applications | Correspondances | Images |
| DFA0011 | Renault Clio | RENAULT:123, SINFA:456 | DFA0011.jpg |

## 🎨 Design Appliqué V4

- ✅ **Logo Dynamic Access** zoomé (logo.png ou texte)
- ✅ **Titre de catégorie** avec nom de la feuille Excel
- ✅ **8 produits par page** en grille 2x4 (comme votre image)
- ✅ **Images des produits ZOOMÉES** (pas d'espace blanc)
- ✅ **Texte français** partout (pas d'arabe)
- ✅ **Direction LTR** pour le français
- ✅ **En-tête gris foncé** avec logo Dynamic
- ✅ **Bandeau rouge** avec texte français
- ✅ **Références en orange**
- ✅ **Spécifications extraites** automatiquement
- ✅ **Numérotation professionnelle**

## 🔧 Extraction Intelligente

Le système extrait automatiquement ces références de la colonne "Correspondances" :
- **OEM** (RENAULT:)
- **SINFA** (SINFA:)
- **MECAFILTRE** (MECAFILTRE:)
- **KNECHT** (KNECHT:)
- **MISFAT** (MISFAT:)
- **MANN** (MANN:)
- **FILTRON** (FILTRON:)

## 📋 Fichiers Principaux V4

- **`generate-catalogue-dynamic-v4.js`** : Générateur V4 final avec toutes les améliorations
- **`lance-dynamic-v4.js`** : Lanceur V4 simple et rapide
- **`GUIDE-DYNAMIC-V4.md`** : Guide détaillé d'utilisation V4

## 🌟 Avantages de la V4

### ✅ Layout Parfait
- **8 produits par page** en grille 2x4
- **Identique à votre image** de référence
- **Dimensions optimisées** pour 8 produits
- **Calcul automatique** des pages

### ✅ Images Parfaites
- **Images des produits** zoomées pour remplir le frame
- **Logo zoomé** pour remplir le frame
- **Aucun espace blanc** autour des images

### ✅ Titre de Catégorie Correct
- **Nom de la feuille Excel** utilisé automatiquement
- **Exemple** : "Filtre à Air Européennes"
- **Design moderne** et professionnel

### ✅ Texte Français
- **Supprimé l'arabe** par défaut
- **Direction LTR** pour le français
- **Bandeau en français** : "Filtre à air"
- **Tous les styles** en français

### ✅ Design Moderne
- **Titres de catégorie** avec style gris foncé
- **Motifs décoratifs** modernes
- **Layout professionnel** optimisé

## 🎉 Résultat Final

Un catalogue professionnel parfait avec le design **Dynamic Access V4** :
- En-tête avec logo zoomé
- Titres de catégorie basés sur vos feuilles Excel
- **8 produits par page** en grille 2x4 (comme votre image)
- Images des produits zoomées (pas d'espace blanc)
- Texte français avec direction LTR
- Design moderne et professionnel
- Prêt pour l'impression

---

## 📖 Documentation Complète

Consultez `GUIDE-DYNAMIC-V4.md` pour des instructions détaillées et le dépannage.

## 🔄 Mise à Jour depuis V3

Si vous utilisez déjà la V3, la V4 corrige :
- ✅ **8 produits par page** (2x4) au lieu de 6 (2x3)
- ✅ Layout identique à votre image
- ✅ Calcul automatique des pages pour 8 produits
- ✅ Dimensions optimisées pour 8 produits
- ✅ Toutes les corrections V3 conservées

## 🎯 Utilisation Recommandée

**Utilisez la V4** pour un résultat parfait :
- **8 produits par page** comme votre image
- Images zoomées sans espace blanc
- Logo zoomé parfaitement
- Titre de catégorie correct
- Texte français partout
- Design moderne et professionnel
