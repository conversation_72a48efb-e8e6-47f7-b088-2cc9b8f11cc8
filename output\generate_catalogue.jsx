// Script InDesign ultra-simplifié pour générer le catalogue Dynamic Access
// Ce script doit être exécuté dans InDesign

// Fonction pour créer un document s'il n'existe pas
function createDocument() {
    var doc = app.activeDocument;
    if (!doc) {
        doc = app.documents.add();
        doc.documentPreferences.pageWidth = "210mm";
        doc.documentPreferences.pageHeight = "297mm";
        doc.documentPreferences.facingPages = false;
    }
    return doc;
}

// Fonction pour créer des styles de paragraphe
function createStyles(doc) {
    var titleStyle, subtitleStyle, productStyle, refStyle, categoryStyle;
    
    // Créer le style Titre
    try {
        titleStyle = doc.paragraphStyles.itemByName("Titre");
        if (!titleStyle || titleStyle.isValid === false) {
            titleStyle = doc.paragraphStyles.add();
            titleStyle.name = "Titre";
            titleStyle.appliedFont = app.fonts.itemByName("Arial");
            titleStyle.fontStyle = "Bold";
            titleStyle.pointSize = 18;
            titleStyle.fillColor = doc.colors.itemByName("Black");
        }
    } catch (e) {
        titleStyle = doc.paragraphStyles.add();
        titleStyle.name = "Titre";
        titleStyle.appliedFont = app.fonts.itemByName("Arial");
        titleStyle.fontStyle = "Bold";
        titleStyle.pointSize = 18;
        titleStyle.fillColor = doc.colors.itemByName("Black");
    }
    
    // Créer le style Sous-titre
    try {
        subtitleStyle = doc.paragraphStyles.itemByName("Sous-titre");
        if (!subtitleStyle || subtitleStyle.isValid === false) {
            subtitleStyle = doc.paragraphStyles.add();
            subtitleStyle.name = "Sous-titre";
            subtitleStyle.appliedFont = app.fonts.itemByName("Arial");
            subtitleStyle.pointSize = 14;
            subtitleStyle.fillColor = doc.colors.itemByName("Black");
        }
    } catch (e) {
        subtitleStyle = doc.paragraphStyles.add();
        subtitleStyle.name = "Sous-titre";
        subtitleStyle.appliedFont = app.fonts.itemByName("Arial");
        subtitleStyle.pointSize = 14;
        subtitleStyle.fillColor = doc.colors.itemByName("Black");
    }
    
    // Créer le style Produit
    try {
        productStyle = doc.paragraphStyles.itemByName("Produit");
        if (!productStyle || productStyle.isValid === false) {
            productStyle = doc.paragraphStyles.add();
            productStyle.name = "Produit";
            productStyle.appliedFont = app.fonts.itemByName("Arial");
            productStyle.pointSize = 9;
            productStyle.fillColor = doc.colors.itemByName("Black");
        }
    } catch (e) {
        productStyle = doc.paragraphStyles.add();
        productStyle.name = "Produit";
        productStyle.appliedFont = app.fonts.itemByName("Arial");
        productStyle.pointSize = 9;
        productStyle.fillColor = doc.colors.itemByName("Black");
    }
    
    // Créer le style Référence (orange)
    try {
        refStyle = doc.paragraphStyles.itemByName("Référence");
        if (!refStyle || refStyle.isValid === false) {
            refStyle = doc.paragraphStyles.add();
            refStyle.name = "Référence";
            refStyle.appliedFont = app.fonts.itemByName("Arial");
            refStyle.fontStyle = "Bold";
            refStyle.pointSize = 12;
            // Créer une couleur orange
            var orangeColor = doc.colors.add();
            orangeColor.name = "Orange";
            orangeColor.colorValue = [0, 50, 100, 0]; // Orange
            refStyle.fillColor = orangeColor;
        }
    } catch (e) {
        refStyle = doc.paragraphStyles.add();
        refStyle.name = "Référence";
        refStyle.appliedFont = app.fonts.itemByName("Arial");
        refStyle.fontStyle = "Bold";
        refStyle.pointSize = 12;
        // Créer une couleur orange
        var orangeColor = doc.colors.add();
        orangeColor.name = "Orange";
        orangeColor.colorValue = [0, 50, 100, 0]; // Orange
        refStyle.fillColor = orangeColor;
    }
    
    // Créer le style Catégorie (orange)
    try {
        categoryStyle = doc.paragraphStyles.itemByName("Catégorie");
        if (!categoryStyle || categoryStyle.isValid === false) {
            categoryStyle = doc.paragraphStyles.add();
            categoryStyle.name = "Catégorie";
            categoryStyle.appliedFont = app.fonts.itemByName("Arial");
            categoryStyle.fontStyle = "Bold";
            categoryStyle.pointSize = 14;
            // Utiliser la couleur orange existante ou en créer une
            try {
                var orangeColor = doc.colors.itemByName("Orange");
                if (!orangeColor || orangeColor.isValid === false) {
                    orangeColor = doc.colors.add();
                    orangeColor.name = "Orange";
                    orangeColor.colorValue = [0, 50, 100, 0]; // Orange
                }
                categoryStyle.fillColor = orangeColor;
            } catch (e) {
                categoryStyle.fillColor = doc.colors.itemByName("Black");
            }
        }
    } catch (e) {
        categoryStyle = doc.paragraphStyles.add();
        categoryStyle.name = "Catégorie";
        categoryStyle.appliedFont = app.fonts.itemByName("Arial");
        categoryStyle.fontStyle = "Bold";
        categoryStyle.pointSize = 14;
        // Utiliser la couleur orange existante ou en créer une
        try {
            var orangeColor = doc.colors.itemByName("Orange");
            if (!orangeColor || orangeColor.isValid === false) {
                orangeColor = doc.colors.add();
                orangeColor.name = "Orange";
                orangeColor.colorValue = [0, 50, 100, 0]; // Orange
            }
            categoryStyle.fillColor = orangeColor;
        } catch (e) {
            categoryStyle.fillColor = doc.colors.itemByName("Black");
        }
    }
    
    return { 
        titleStyle: titleStyle, 
        subtitleStyle: subtitleStyle, 
        productStyle: productStyle,
        refStyle: refStyle,
        categoryStyle: categoryStyle
    };
}

// Fonction pour créer la page de couverture
function createCoverPage(doc, styles) {
    var coverPage = doc.pages[0];
    
    // Titre principal
    var titleFrame = coverPage.textFrames.add();
    titleFrame.geometricBounds = [100, 20, 150, 190];
    titleFrame.contents = "CATALOGUE DYNAMIC ACCESS";
    titleFrame.paragraphs[0].appliedParagraphStyle = styles.titleStyle;
    titleFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Sous-titre
    var subtitleFrame = coverPage.textFrames.add();
    subtitleFrame.geometricBounds = [160, 20, 180, 190];
    subtitleFrame.contents = "Filtres de Qualité et Performance";
    subtitleFrame.paragraphs[0].appliedParagraphStyle = styles.subtitleStyle;
    subtitleFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Date
    var dateFrame = coverPage.textFrames.add();
    dateFrame.geometricBounds = [250, 20, 270, 190];
    dateFrame.contents = "2024";
    dateFrame.paragraphs[0].appliedParagraphStyle = styles.subtitleStyle;
    dateFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
}

// Fonction pour générer les pages de produits
function generateProductPages(products, categoryName) {
    var doc = app.activeDocument;
    var styles = createStyles(doc);
    
    var pageWidth = doc.documentPreferences.pageWidth;
    var productsPerPage = 6;
    var currentPage = null;
    
    // Créer la première page pour la catégorie
    currentPage = doc.pages.add();
    
    // En-tête de page avec logo
    try {
        var logoPath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/logo.png";
        var logoFile = new File(logoPath);
        if (logoFile.exists) {
            var logoFrame = currentPage.rectangles.add();
            logoFrame.geometricBounds = [20, 20, 50, 100];
            logoFrame.place(File(logoPath));
            logoFrame.fit(FittingOptions.CONTENT_TO_FRAME);
        }
    } catch (e) {
        // Ignorer si le logo n'existe pas
    }
    
    // Catégorie en haut à droite
    var categoryFrame = currentPage.textFrames.add();
    categoryFrame.geometricBounds = [20, pageWidth - 150, 40, pageWidth - 20];
    categoryFrame.contents = categoryName;
    categoryFrame.paragraphs[0].appliedParagraphStyle = styles.categoryStyle;
    categoryFrame.paragraphs[0].justification = Justification.RIGHT_ALIGN;
    
    // Configuration de la grille pour 6 produits (3x2)
    var gridStartY = 70;
    var gridStartX = 20;
    var productWidth = 60;
    var productHeight = 120;
    var marginX = 10;
    var marginY = 20;
    
    for (var i = 0; i < products.length; i++) {
        var product = products[i];
        
        // Vérifier si on doit créer une nouvelle page
        if (i > 0 && i % productsPerPage === 0) {
            currentPage = doc.pages.add();
            
            // En-tête de la nouvelle page avec logo
            try {
                var newLogoPath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/logo.png";
                var newLogoFile = new File(newLogoPath);
                if (newLogoFile.exists) {
                    var newLogoFrame = currentPage.rectangles.add();
                    newLogoFrame.geometricBounds = [20, 20, 50, 100];
                    newLogoFrame.place(File(newLogoPath));
                    newLogoFrame.fit(FittingOptions.CONTENT_TO_FRAME);
                }
            } catch (e) {
                // Ignorer si le logo n'existe pas
            }
            
            var newCategoryFrame = currentPage.textFrames.add();
            newCategoryFrame.geometricBounds = [20, pageWidth - 150, 40, pageWidth - 20];
            newCategoryFrame.contents = categoryName;
            newCategoryFrame.paragraphs[0].appliedParagraphStyle = styles.categoryStyle;
            newCategoryFrame.paragraphs[0].justification = Justification.RIGHT_ALIGN;
        }
        
        // Calculer la position dans la grille (3x2)
        var pageIndex = i % productsPerPage;
        var row = Math.floor(pageIndex / 3);
        var col = pageIndex % 3;
        
        var x = gridStartX + col * (productWidth + marginX);
        var y = gridStartY + row * (productHeight + marginY);
        
        // Créer le frame de la référence (en haut)
        var refFrame = currentPage.textFrames.add();
        refFrame.geometricBounds = [y, x, y + 15, x + productWidth];
        refFrame.contents = product.Référence || "";
        refFrame.paragraphs[0].appliedParagraphStyle = styles.refStyle;
        
        // Créer le frame des applications (à gauche de l'image)
        var appFrame = currentPage.textFrames.add();
        appFrame.geometricBounds = [y + 20, x, y + 80, x + productWidth/2 - 2];
        var appContent = "Applications:\n" + (product.Applications || "");
        appFrame.contents = appContent;
        appFrame.paragraphs[0].appliedParagraphStyle = styles.productStyle;
        
        // Créer le frame des correspondances (à droite de l'image)
        var corrFrame = currentPage.textFrames.add();
        corrFrame.geometricBounds = [y + 20, x + productWidth/2 + 2, y + 80, x + productWidth];
        var corrContent = "Correspondances:\n" + (product.Correspondances || "Aucune correspondance");
        corrFrame.contents = corrContent;
        corrFrame.paragraphs[0].appliedParagraphStyle = styles.productStyle;
        
        // Créer le frame de l'image (en bas)
        if (product.Images) {
            try {
                var imagePath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/" + product.Images;
                var imageFile = new File(imagePath);
                if (imageFile.exists) {
                    var imageFrame = currentPage.rectangles.add();
                    imageFrame.geometricBounds = [y + 85, x + 5, y + 115, x + productWidth - 5];
                    imageFrame.place(File(imagePath));
                    imageFrame.fit(FittingOptions.CONTENT_TO_FRAME);
                    imageFrame.sendToBack();
                }
            } catch (e) {
                // Ignorer si l'image n'existe pas
            }
        }
    }
}

// Début du script principal
var doc = createDocument();
var styles = createStyles(doc);

// Créer la page de couverture
createCoverPage(doc, styles);

// Données des produits simplifiées
var products = [
  {
    Référence: "DFA0011",
    Applications: "Renault Clio 1.9 D (64 HP) Clio 1.8i RT, Initiale (90 HP) Clio 1.7 RT, Baccara (92 HP) Clio 1.8i RT, RSI (95/110 HP) Express 1.9 D (65 HP)",
    Correspondances: "RENAULT: 7701034705\nASZ: ASZ1011\nSINFA: 22877\nMECAFILTRE: EL3413\nKNECHT: LX329\nMISFAT: R194\nMANN: C16113\nFILTRON: AR282",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    Applications: "Renault Clio 1.9 D (64 HP) Clio 1.8i RT, Initiale (90 HP) Clio 1.7 RT, Baccara (92 HP) Clio 1.8i RT, RSI (95/110 HP) Express 1.9 D (65 HP)",
    Correspondances: "RENAULT: 7701034705\nASZ: ASZ1011\nSINFA: 22877\nMECAFILTRE: EL3413\nKNECHT: LX329\nMISFAT: R194\nMANN: C16113\nFILTRON: AR282",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    Applications: "Renault Clio 1.9 D (64 HP) Clio 1.8i RT, Initiale (90 HP) Clio 1.7 RT, Baccara (92 HP) Clio 1.8i RT, RSI (95/110 HP) Express 1.9 D (65 HP)",
    Correspondances: "RENAULT: 7701034705\nASZ: ASZ1011\nSINFA: 22877\nMECAFILTRE: EL3413\nKNECHT: LX329\nMISFAT: R194\nMANN: C16113\nFILTRON: AR282",
    Images: "DFA0011.jpg"
  }
];

generateProductPages(products, "Filtre à Air Européennes");

alert("Catalogue Dynamic Access généré avec succès !");
