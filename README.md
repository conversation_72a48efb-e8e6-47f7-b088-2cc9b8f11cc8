# 🚀 Catalogue Dynamic Access - Guide d'utilisation

## 📋 Vue d'ensemble
Système automatique de génération de catalogue InDesign synchronisé avec Excel. Permet de gérer facilement les produits et de générer des catalogues professionnels avec mise en page automatique.

## 🏗️ Structure du projet
```
Dynamic-Access/
├── excel/                    # Fichier Excel des produits
│   └── catalogue.xlsx
├── images/                   # Images des produits
├── templates/                # Templates InDesign
├── output/                   # Fichiers générés
├── package.json             # Dépendances Node.js
├── generate-catalogue.js    # Générateur de catalogue
├── manage-products.js       # Gestionnaire de produits
└── README.md               # Ce guide
```

## 🚀 Installation

### 1. Installer Node.js
Téléchargez et installez Node.js depuis [nodejs.org](https://nodejs.org/)

### 2. Installer les dépendances
```bash
npm install
```

## 📊 Structure du fichier Excel

Votre fichier `excel/catalogue.xlsx` doit contenir ces colonnes :

| Reference | Nom | Categorie | SousCategorie | Description | Equivalent | Image |
|-----------|-----|-----------|---------------|-------------|------------|-------|
| DFA1101 | Filtre à air européen | Filtre air | Européenne | Description... | FAE001 | filtre_air_001.jpg |
| DFA1102 | Filtre à air premium | Filtre air | Européenne | Description... | FAE002 | filtre_air_002.jpg |

### Catégories recommandées :
- **Filtre air** : Filtres à air
- **Gasoil** : Filtres à gasoil
- **Huile** : Filtres à huile
- **Habitacle** : Filtres d'habitacle
- **Poids lourds** : Filtres poids lourds

## 🛠️ Utilisation

### 🚀 Première utilisation (avec votre fichier Excel existant)

#### 1. Convertir votre fichier Excel
```bash
npm run convert-excel
```
Cette commande lit automatiquement votre fichier Excel avec tous ses onglets et le convertit vers le format du système.

#### 2. Vérifier la conversion
Le système crée automatiquement :
- `excel/catalogue_converted.xlsx` - Votre fichier converti
- `output/catalogue_config.json` - Configuration du catalogue

#### 3. Générer le catalogue
```bash
npm run generate
```

### Gestion des produits

#### Ajouter un produit
```bash
npm run add-product
```
Suivez les instructions pour saisir les informations du produit.

#### Modifier un produit
```bash
npm run update-product
```
Entrez la référence du produit à modifier, puis saisissez les nouvelles valeurs.

#### Supprimer un produit
```bash
npm run delete-product
```
Entrez la référence du produit à supprimer et confirmez.

#### Lister tous les produits
```bash
npm run list-products
```
Affiche un tableau de tous les produits du catalogue.

### Génération du catalogue

#### Générer le catalogue complet
```bash
npm run generate
```

Cette commande :
1. Lit le fichier Excel
2. Respecte l'ordre exact des lignes
3. Génère les fichiers de configuration InDesign
4. Crée un script InDesign automatique

## 📱 Ordre des produits

**IMPORTANT** : L'ordre des produits dans le catalogue respecte exactement l'ordre de vos lignes Excel.

### Exemple d'insertion :
Si vous avez :
```
DFA1101
DFA1102
DFA1105
DFA1106
DFA1107
```

Et que vous insérez DFA1103 et DFA1104 entre DFA1102 et DFA1105 :
```
DFA1101
DFA1102
DFA1103  ← Nouveau
DFA1104  ← Nouveau
DFA1105
DFA1106
DFA1107
```

Le catalogue se mettra automatiquement à jour avec le bon ordre !

## 🎨 Mise en page automatique

Le système génère automatiquement :
- **8 produits par page** (configurable)
- **Organisation par catégories** et sous-catégories
- **Mise en page cohérente** avec styles prédéfinis
- **Liens automatiques** vers les images

## 📁 Organisation des images

Placez vos images dans le dossier `images/` :
```
images/
├── filtre_air_001.jpg
├── filtre_air_002.jpg
├── filtre_gasoil_001.jpg
└── ...
```

## 🔄 Workflow recommandé

1. **Gérer les produits** via Excel ou les commandes
2. **Générer le catalogue** avec `npm run generate`
3. **Ouvrir dans InDesign** pour personnaliser le design
4. **Ajouter de nouveaux produits** quand nécessaire
5. **Régénérer** pour mettre à jour le contenu

## ⚠️ Notes importantes

- **Ordre Excel = Ordre catalogue** : Respectez l'ordre de vos lignes
- **Images** : Assurez-vous que les noms correspondent à ceux du fichier Excel
- **Catégories** : Utilisez exactement les mêmes noms dans Excel
- **Sauvegarde** : Gardez une copie de sauvegarde de votre fichier Excel

## 🆘 Dépannage

### Erreur "Fichier Excel non trouvé"
- Vérifiez que le fichier `excel/catalogue.xlsx` existe
- Utilisez `npm run add-product` pour créer le fichier initial

### Erreur de dépendances
```bash
npm install
```

### Problème d'images
- Vérifiez que les noms d'images correspondent à ceux du fichier Excel
- Assurez-vous que les images sont dans le dossier `images/`

## 📞 Support

Pour toute question ou problème, vérifiez :
1. La structure de votre fichier Excel
2. Les noms des catégories
3. Les noms des fichiers d'images
4. L'installation de Node.js

---

**🎯 Objectif** : Créer et maintenir facilement un catalogue professionnel qui s'adapte automatiquement à vos besoins !
