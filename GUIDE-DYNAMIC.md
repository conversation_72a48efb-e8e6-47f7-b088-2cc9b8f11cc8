# Guide Catalogue Dynamic

## 🎯 Objectif
<PERSON> automatiquement un catalogue InDesign avec le design Dynamic Access à partir de votre fichier Excel existant.

## 📋 Prérequis
- Node.js installé
- Adobe InDesign
- Votre fichier Excel `excel/catalogue.xlsx`

## 📁 Structure Excel Requise
Votre fichier `excel/catalogue.xlsx` doit contenir ces colonnes :
- **Colonne A** : Référence (ex: ASZ1008)
- **Colonne B** : Applications véhicules
- **Colonne C** : Correspondances (format: RENAULT:123, SINFA:456, MECAFILTRE:789)
- **Colonne D** : Nom du fichier image (ex: DFA0011.jpg)

## 🚀 Utilisation

### 1. Préparer les données
- Placez votre fichier Excel dans le dossier `excel/catalogue.xlsx`
- Placez vos images dans le dossier `images/`

### 2. Générer le catalogue
```bash
node lance-dynamic.js
```

### 3. <PERSON><PERSON><PERSON> le catalogue dans InDesign
1. Ouvrir Adobe InDesign
2. <PERSON><PERSON> dans **Fichier > Scripts > Exécuter le script**
3. Sélectionner : `output/generate_catalogue_dynamic.jsx`
4. Le catalogue sera créé automatiquement

## 🎨 Design Appliqué
- **Logo** : "Dynamic Access" (au lieu d'ASZ)
- **En-tête** : Zone grise avec logo Dynamic
- **Bandeau** : Rouge avec texte bilingue
- **Produits** : 6 par page en grille 2x3
- **Références** : En orange
- **Spécifications** : Extraites automatiquement des correspondances
- **Images** : Intégrées automatiquement
- **Numérotation** : Pages numérotées

## 🔧 Extraction Automatique
Le système extrait automatiquement ces références de la colonne "Correspondances" :
- OEM (RENAULT:)
- SINFA (SINFA:)
- MECAFILTRE (MECAFILTRE:)
- KNECHT (KNECHT:)
- MISFAT (MISFAT:)
- MANN (MANN:)
- FILTRON (FILTRON:)

## 📁 Fichiers Créés
- `output/generate_catalogue_dynamic.jsx` : Script InDesign

## ❗ Dépannage
- **Erreur Excel** : Vérifiez que les colonnes sont dans le bon ordre
- **Images manquantes** : Vérifiez que les fichiers sont dans le dossier `images/`
- **Erreur InDesign** : Vérifiez que le script est bien généré dans `output/`

## 🎉 Résultat
Un catalogue professionnel avec le design Dynamic Access, prêt à imprimer !
