// Script InDesign pour Catalogue Dynamic
// Généré automatiquement pour votre structure Excel existante

// Fonction pour créer un nouveau document
function createDocument() {
    var doc = app.documents.add();
    doc.documentPreferences.pageWidth = "210mm";
    doc.documentPreferences.pageHeight = "297mm";
    doc.documentPreferences.facingPages = false;
    doc.documentPreferences.pageOrientation = PageOrientation.PORTRAIT;
    
    // Créer les couleurs nécessaires
    createColors(doc);
    
    return doc;
}

// Fonction pour créer les couleurs
function createColors(doc) {
    // Couleur rouge pour les bandeaux
    try {
        var redColor = doc.colors.itemByName("Rouge");
        if (!redColor || redColor.isValid === false) {
            redColor = doc.colors.add();
            redColor.name = "Rouge";
            redColor.colorValue = [0, 100, 100, 0]; // Rouge
        }
    } catch (e) {
        redColor = doc.colors.add();
        redColor.name = "Rouge";
        redColor.colorValue = [0, 100, 100, 0];
    }
    
    // Couleur orange pour les références
    try {
        var orangeColor = doc.colors.itemByName("Orange");
        if (!orangeColor || orangeColor.isValid === false) {
            orangeColor = doc.colors.add();
            orangeColor.name = "Orange";
            orangeColor.colorValue = [0, 50, 100, 0]; // Orange
        }
    } catch (e) {
        orangeColor = doc.colors.add();
        orangeColor.name = "Orange";
        orangeColor.colorValue = [0, 50, 100, 0];
    }
    
    // Couleur grise pour le logo
    try {
        var grayColor = doc.colors.itemByName("Gris");
        if (!grayColor || grayColor.isValid === false) {
            grayColor = doc.colors.add();
            grayColor.name = "Gris";
            grayColor.colorValue = [0, 0, 0, 50]; // Gris 50%
        }
    } catch (e) {
        grayColor = doc.colors.add();
        grayColor.name = "Gris";
        grayColor.colorValue = [0, 0, 0, 50];
    }
}

// Fonction pour créer les styles
function createStyles(doc) {
    // Style pour le titre principal
    var titleStyle = doc.paragraphStyles.add();
    titleStyle.name = "TitrePrincipal";
    titleStyle.appliedFont = app.fonts.itemByName("Arial");
    titleStyle.fontStyle = "Bold";
    titleStyle.pointSize = 24; // Utiliser pointSize au lieu de fontSize
    titleStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les références Dynamic
    var refStyle = doc.paragraphStyles.add();
    refStyle.name = "ReferenceDynamic";
    refStyle.appliedFont = app.fonts.itemByName("Arial");
    refStyle.fontStyle = "Bold";
    refStyle.pointSize = 14; // Utiliser pointSize au lieu de fontSize
    refStyle.fillColor = doc.colors.itemByName("Orange");
    
    // Style pour les spécifications
    var specStyle = doc.paragraphStyles.add();
    specStyle.name = "Specifications";
    specStyle.appliedFont = app.fonts.itemByName("Arial");
    specStyle.pointSize = 8; // Utiliser pointSize au lieu de fontSize
    specStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les applications
    var appStyle = doc.paragraphStyles.add();
    appStyle.name = "Applications";
    appStyle.appliedFont = app.fonts.itemByName("Arial");
    appStyle.pointSize = 7; // Utiliser pointSize au lieu de fontSize
    appStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour le bandeau rouge
    var bandeauStyle = doc.paragraphStyles.add();
    bandeauStyle.name = "BandeauRouge";
    bandeauStyle.appliedFont = app.fonts.itemByName("Arial");
    bandeauStyle.fontStyle = "Bold";
    bandeauStyle.pointSize = 12; // Utiliser pointSize au lieu de fontSize
    bandeauStyle.fillColor = doc.colors.itemByName("Paper");
    
    return {
        titleStyle: titleStyle,
        refStyle: refStyle,
        specStyle: specStyle,
        appStyle: appStyle,
        bandeauStyle: bandeauStyle
    };
}

// Fonction pour créer l'en-tête de page
function createPageHeader(page, styles) {
    // Logo Dynamic (zone grise)
    var logoFrame = page.rectangles.add();
    logoFrame.geometricBounds = [10, 10, 30, 80];
    logoFrame.fillColor = doc.colors.itemByName("Gris");
    
    // Texte Dynamic Access
    var logoText = page.textFrames.add();
    logoText.geometricBounds = [12, 15, 28, 75];
    logoText.contents = "Dynamic\nAccess";
    logoText.paragraphs[0].appliedParagraphStyle = styles.titleStyle;
    logoText.paragraphs[0].justification = Justification.CENTER_ALIGN;
    logoText.paragraphs[0].fillColor = doc.colors.itemByName("Paper");
    
    // Bandeau rouge
    var bandeauFrame = page.rectangles.add();
    bandeauFrame.geometricBounds = [10, 150, 30, 200];
    bandeauFrame.fillColor = doc.colors.itemByName("Rouge");
    
    // Texte du bandeau
    var bandeauText = page.textFrames.add();
    bandeauText.geometricBounds = [12, 155, 28, 195];
    bandeauText.contents = "RENAULT - DACIA - CITROEN - PEUGEOT\nمصفاة هواء / Filtre à air";
    bandeauText.paragraphs[0].appliedParagraphStyle = styles.bandeauStyle;
    bandeauText.paragraphs[0].justification = Justification.CENTER_ALIGN;
}

// Fonction pour créer un produit individuel
function createProduct(page, product, x, y, width, height, styles) {
    // Cadre du produit
    var productFrame = page.rectangles.add();
    productFrame.geometricBounds = [y, x, y + height, x + width];
    productFrame.strokeColor = doc.colors.itemByName("Black");
    productFrame.strokeWeight = 0.5;
    
    // Référence Dynamic (en haut)
    var refFrame = page.textFrames.add();
    refFrame.geometricBounds = [y + 2, x + 2, y + 12, x + width - 2];
    refFrame.contents = product.Référence;
    refFrame.paragraphs[0].appliedParagraphStyle = styles.refStyle;
    
    // Spécifications (à gauche)
    var specFrame = page.textFrames.add();
    specFrame.geometricBounds = [y + 15, x + 2, y + 45, x + width/2 - 2];
    var specContent = "OEM: " + (product.OEM || '') + "\n";
    specContent += "SINFA: " + (product.SINFA || '') + "\n";
    specContent += "MECAFILTRE: " + (product.MECAFILTRE || '') + "\n";
    specContent += "KNECHT: " + (product.KNECHT || '') + "\n";
    specContent += "MISFAT: " + (product.MISFAT || '') + "\n";
    specContent += "MANN: " + (product.MANN || '') + "\n";
    specContent += "FILTRON: " + (product.FILTRON || '');
    specFrame.contents = specContent;
    specFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    
    // Image du produit (au centre)
    if (product.Images) {
        try {
            var imagePath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/" + product.Images;
            var imageFile = new File(imagePath);
            if (imageFile.exists) {
                var imageFrame = page.rectangles.add();
                imageFrame.geometricBounds = [y + 15, x + width/2 + 2, y + 45, x + width - 2];
                imageFrame.place(File(imagePath));
                imageFrame.fit(FittingOptions.FILL_FRAME_PROPORTIONALLY);
                imageFrame.fit(FittingOptions.CENTER_CONTENT);
            }
        } catch (e) {
            // Si l'image n'existe pas, on continue
        }
    }
    
    // Applications (en bas)
    var appFrame = page.textFrames.add();
    appFrame.geometricBounds = [y + 50, x + 2, y + height - 2, x + width - 2];
    appFrame.contents = product.Applications || '';
    appFrame.paragraphs[0].appliedParagraphStyle = styles.appStyle;
}

// Fonction pour créer une page de produits
function createProductPage(doc, products, pageNumber, styles) {
    var page = doc.pages.add();
    
    // Créer l'en-tête
    createPageHeader(page, styles);
    
    // Dimensions pour 6 produits (2x3)
    var pageWidth = 210; // mm
    var pageHeight = 297; // mm
    var margin = 10; // mm
    var productWidth = (pageWidth - 3 * margin) / 2; // 2 colonnes
    var productHeight = (pageHeight - 4 * margin - 30) / 3; // 3 rangées, moins l'en-tête
    
    // Position de départ (après l'en-tête)
    var startY = 40;
    var startX = margin;
    
    // Créer les 6 produits
    for (var i = 0; i < Math.min(6, products.length); i++) {
        var row = Math.floor(i / 2);
        var col = i % 2;
        
        var x = startX + col * (productWidth + margin);
        var y = startY + row * (productHeight + margin);
        
        createProduct(page, products[i], x, y, productWidth, productHeight, styles);
    }
    
    // Numéro de page
    var pageNumFrame = page.textFrames.add();
    pageNumFrame.geometricBounds = [pageHeight - 15, pageWidth/2 - 10, pageHeight - 10, pageWidth/2 + 10];
    pageNumFrame.contents = "-" + pageNumber + "-";
    pageNumFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    pageNumFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Lignes décoratives autour du numéro de page
    var line1 = page.graphicLines.add();
    line1.strokeColor = doc.colors.itemByName("Rouge");
    line1.strokeWeight = 1;
    line1.geometricBounds = [pageHeight - 12, pageWidth/2 - 20, pageHeight - 12, pageWidth/2 - 15];
    
    var line2 = page.graphicLines.add();
    line2.strokeColor = doc.colors.itemByName("Rouge");
    line2.strokeWeight = 1;
    line2.geometricBounds = [pageHeight - 12, pageWidth/2 + 15, pageHeight - 12, pageWidth/2 + 20];
}

// Début du script principal
var doc = createDocument();
var styles = createStyles(doc);

// Données des produits

// Catégorie: DFA00100
var productsDFA00100 = [
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
];
createProductPage(doc, productsDFA00100.slice(0, 6), 1, styles);
createProductPage(doc, productsDFA00100.slice(6, 12), 2, styles);



alert("Catalogue Dynamic généré avec succès !");
