{"name": "dynamic-access-catalogue", "version": "1.0.0", "description": "Système de génération automatique de catalogue InDesign synchronisé avec Excel", "main": "generate-catalogue.js", "scripts": {"setup": "node setup.js", "convert-excel": "node excel-converter.js", "convert-excel-simple": "node excel-converter-simple.js", "test-conversion": "node test-conversion.js", "generate": "node generate-catalogue.js", "add-product": "node manage-products.js add", "update-product": "node manage-products.js update", "delete-product": "node manage-products.js delete", "list-products": "node manage-products.js list"}, "dependencies": {"xlsx": "^0.18.5", "fs-extra": "^11.1.1", "path": "^0.12.7", "chalk": "^4.1.2"}, "keywords": ["indesign", "catalogue", "excel", "automation"], "author": "Dynamic Access", "license": "MIT"}