# 📊 Organisation de votre fichier Excel source

## 📋 Structure attendue

Votre fichier Excel doit contenir ces **onglets** (un par catégorie) :

| Onglet | Description | Exemple |
|--------|-------------|---------|
| F, Air EU | Filtres à air européens | RENAULT, PEUGEOT, CITROEN... |
| F,Air AS | Filtres à air asiatiques | TOYOTA, HONDA, NISSAN... |
| Gasoil EU | Filtres à gasoil européens | RENAULT, PEUGEOT, CITROEN... |
| Gasoil AS | Filtres à gasoil asiatiques | TOYOTA, HONDA, NISSAN... |
| Huile EU | Filtres à huile européens | RENAULT, PEUGEOT, CITROEN... |
| Huile AS | Filtres à huile asiatiques | TOYOTA, HONDA, NISSAN... |
| F, AIR PL | Filtres à air poids lourds | FORD Transit, IVECO... |
| HABITACLE | Filtres d'habitacle | Tous véhicules |

## 🔧 Colonnes dans chaque onglet

Chaque onglet doit avoir ces **5 colonnes** dans cet ordre :

| Colonne | Contenu | Exemple |
|---------|---------|---------|
| **A - Marque** | Marque du véhicule | RENAULT, PEUGEOT, CITROEN |
| **B - Référence** | Référence du filtre | DFA0011, DFA0018, DFA0144 |
| **C - Applications** | Modèles compatibles | Clio 1.9 D (64 HP), Clio 1.8i RT... |
| **D - Correspondances** | Références équivalentes | RENAULT, ASZ, SINFA, MECAFILTRE... |
| **E - Images** | Nom du fichier image | DFA0011, DFA0018, DFA0144 |

## 📝 Exemple de données

### Onglet "F, Air EU"
```
| Marque    | Référence | Applications                    | Correspondances                    | Images  |
|-----------|-----------|--------------------------------|-----------------------------------|---------|
| RENAULT   | DFA0011   | Clio 1.9 D (64 HP)            | RENAULT, ASZ, SINFA, MECAFILTRE  | DFA0011 |
|           |           | Clio 1.8i RT                   | KNECHT, MISFAT, MANN, FILTRON    |         |
|           |           | Initiale (90 HP)               |                                    |         |
|           |           | Clio 1.7 RT                    |                                    |         |
|           |           | Baccara (92 HP)                |                                    |         |
|           |           | Clio 1.8i RT, RSI (95/110 HP) |                                    |         |
|           |           | Express 1.9 D (65 HP)          |                                    |         |
```

## ⚠️ Points importants

### ✅ Ce qui fonctionne
- **Cellules fusionnées** : Marque et Référence peuvent être fusionnées
- **Lignes multiples** : Applications et Correspondances sur plusieurs lignes
- **Ordre libre** : L'ordre des lignes est préservé
- **Formats** : .xlsx, .xls

### ❌ Ce qui ne fonctionne pas
- **Colonnes dans le mauvais ordre**
- **Onglets avec des noms différents**
- **Colonnes manquantes**
- **Fichiers .csv** (utilisez .xlsx)

## 🔄 Processus de conversion

1. **Placez** votre fichier dans `excel/catalogue.xlsx`
2. **Lancez** la conversion : `npm run convert-excel`
3. **Vérifiez** le résultat : `excel/catalogue_converted.xlsx`
4. **Générez** le catalogue : `npm run generate`

## 📁 Fichiers créés

Après conversion, vous aurez :
- `excel/catalogue_converted.xlsx` - Fichier converti pour le système
- `output/catalogue_config.json` - Configuration du catalogue
- **Votre fichier original** reste intact

## 🆘 Dépannage

### Erreur "Onglet non reconnu"
- Vérifiez que les noms d'onglets correspondent exactement
- Respectez la casse (majuscules/minuscules)

### Erreur "Colonnes manquantes"
- Vérifiez que vous avez bien 5 colonnes
- Vérifiez l'ordre : Marque, Référence, Applications, Correspondances, Images

### Données manquantes
- Vérifiez que les cellules fusionnées sont correctes
- Vérifiez que toutes les références ont des données

---

**🎯 Objectif** : Organiser votre fichier Excel pour une conversion automatique parfaite !
