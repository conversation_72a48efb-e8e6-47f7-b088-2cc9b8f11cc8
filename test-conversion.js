const ExcelConverter = require('./excel-converter');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class ConversionTester {
    constructor() {
        this.converter = new ExcelConverter();
    }

    async testConversion() {
        try {
            console.log(chalk.blue('🧪 Test de la conversion Excel...\n'));
            
            // Vérifier que le fichier source existe
            const sourcePath = path.join(__dirname, 'excel', 'catalogue.xlsx');
            if (!await fs.pathExists(sourcePath)) {
                console.log(chalk.yellow('⚠️ Fichier Excel source non trouvé'));
                console.log(chalk.gray('   Placez votre fichier Excel dans excel/catalogue.xlsx'));
                console.log(chalk.gray('   Puis relancez : npm run test-conversion'));
                return false;
            }

            // Lancer la conversion
            console.log(chalk.blue('🔄 Lancement de la conversion...'));
            const products = await this.converter.convertExcel();
            
            // Vérifier le résultat
            const convertedPath = path.join(__dirname, 'excel', 'catalogue_converted.xlsx');
            const configPath = path.join(__dirname, 'output', 'catalogue_config.json');
            
            if (await fs.pathExists(convertedPath) && await fs.pathExists(configPath)) {
                console.log(chalk.green('\n✅ Test de conversion réussi !'));
                console.log(chalk.blue(`📊 ${products.length} produits convertis`));
                
                // Afficher un aperçu des produits
                await this.showProductsPreview(products);
                
                return true;
            } else {
                throw new Error('Fichiers de sortie manquants');
            }
            
        } catch (error) {
            console.error(chalk.red('\n❌ Test de conversion échoué :'), error.message);
            return false;
        }
    }

    async showProductsPreview(products) {
        if (products.length === 0) return;
        
        console.log(chalk.blue('\n📋 Aperçu des produits convertis :'));
        console.log(chalk.gray('─'.repeat(80)));
        
        // Afficher les 5 premiers produits
        const previewProducts = products.slice(0, 5);
        
        previewProducts.forEach((product, index) => {
            console.log(chalk.blue(`\n${index + 1}. ${product.Reference}`));
            console.log(chalk.gray(`   Nom: ${product.Nom}`));
            console.log(chalk.gray(`   Catégorie: ${product.Categorie} - ${product.SousCategorie}`));
            console.log(chalk.gray(`   Description: ${product.Description.substring(0, 60)}...`);
            console.log(chalk.gray(`   Équivalent: ${product.Equivalent}`);
            console.log(chalk.gray(`   Image: ${product.Image}`));
        });
        
        if (products.length > 5) {
            console.log(chalk.gray(`\n... et ${products.length - 5} autres produits`));
        }
        
        console.log(chalk.gray('─'.repeat(80)));
    }

    async validateConversion() {
        try {
            console.log(chalk.blue('\n🔍 Validation de la conversion...'));
            
            // Vérifier la structure des produits
            const configPath = path.join(__dirname, 'output', 'catalogue_config.json');
            const config = await fs.readJson(configPath);
            
            const requiredFields = ['Reference', 'Nom', 'Categorie', 'SousCategorie', 'Description', 'Equivalent', 'Image'];
            const sampleProduct = config.products[0];
            
            if (!sampleProduct) {
                throw new Error('Aucun produit trouvé dans la configuration');
            }
            
            // Vérifier que tous les champs requis sont présents
            for (const field of requiredFields) {
                if (!sampleProduct[field]) {
                    throw new Error(`Champ manquant : ${field}`);
                }
            }
            
            console.log(chalk.green('   ✅ Structure des produits validée'));
            
            // Vérifier les catégories
            const categories = Object.keys(config.categories);
            console.log(chalk.green(`   ✅ ${categories.length} catégories détectées`));
            
            // Vérifier le total des produits
            const totalProducts = config.products.length;
            console.log(chalk.green(`   ✅ ${totalProducts} produits au total`));
            
            console.log(chalk.green('\n🎉 Validation terminée avec succès !'));
            return true;
            
        } catch (error) {
            console.error(chalk.red('❌ Validation échouée :'), error.message);
            return false;
        }
    }
}

// Exécution du script
if (require.main === module) {
    const tester = new ConversionTester();
    
    tester.testConversion()
        .then(async (success) => {
            if (success) {
                console.log(chalk.blue('\n🔍 Lancement de la validation...'));
                await tester.validateConversion();
                
                console.log(chalk.blue('\n🚀 Prochaines étapes :'));
                console.log(chalk.gray('1. Vérifiez le fichier converti : excel/catalogue_converted.xlsx'));
                console.log(chalk.gray('2. Générez le catalogue : npm run generate'));
                console.log(chalk.gray('3. Ou testez la gestion : npm run list-products'));
            }
        })
        .catch(error => {
            console.error(chalk.red('\n❌ Test échoué'));
            process.exit(1);
        });
}

module.exports = ConversionTester;
