# Catalogue Dynamic Access V3 - Version Corrigée

## 🎯 Système de Génération de Catalogue Parfait

Ce projet génère automatiquement un catalogue InDesign professionnel avec le design **Dynamic Access V3** corrigé à partir de votre fichier Excel existant, avec images zoomées et texte français.

## ✨ Corrections Appliquées V3

### 🖼️ Images Zoomées
- **Images des produits** : ZOOMÉES pour remplir complètement le frame (pas d'espace blanc)
- **Logo** : ZOOMÉ pour remplir complètement le frame
- **Utilise `FittingOptions.FILL_FRAME`** pour un zoom parfait

### 🏷️ Titre de Catégorie Correct
- **Nom de la feuille Excel** utilisé comme titre de catégorie
- **Exemple** : "Filtre à Air Européennes" (votre feuille Excel)
- **Design moderne** avec couleur gris foncé (80%)

### 🇫🇷 Texte Français par Défaut
- **Supprimé le support arabe** par défaut
- **Direction LTR** (Left-to-Right) pour le français
- **Texte du bandeau** : "Filtre à air" (au lieu du texte arabe)
- **Applications véhicules** en français avec alignement LTR

### 🎨 Design Moderne
- **Titres de catégorie** avec style gris foncé moderne
- **Motifs décoratifs** modernes (lignes rouges, carrés colorés)
- **Layout optimisé** pour l'impression

## 📁 Structure du Projet

```
Dynamic-Access/
├── excel/
│   └── catalogue.xlsx                        # Votre fichier Excel avec les produits
├── images/                                   # Dossier pour les images des produits
│   └── logo.png                             # Logo Dynamic (optionnel)
├── output/
│   └── generate_catalogue_dynamic_v3.jsx    # Script InDesign V3 corrigé
├── generate-catalogue-dynamic-v3.js          # Générateur V3 corrigé
├── lance-dynamic-v3.js                      # Lanceur V3
└── GUIDE-DYNAMIC-V3.md                      # Guide d'utilisation V3
```

## 🚀 Utilisation Rapide

1. **Préparer vos données** :
   - Placez votre fichier Excel dans `excel/catalogue.xlsx`
   - Placez vos images dans le dossier `images/`
   - Placez `logo.png` dans `images/` (optionnel)

2. **Générer le catalogue** :
   ```bash
   node lance-dynamic-v3.js
   ```

3. **Créer dans InDesign** :
   - Ouvrir Adobe InDesign
   - Fichier > Scripts > Exécuter le script
   - Sélectionner : `output/generate_catalogue_dynamic_v3.jsx`

## 📊 Format Excel Requis

| Colonne A | Colonne B | Colonne C | Colonne D |
|-----------|-----------|-----------|-----------|
| Référence | Applications | Correspondances | Images |
| DFA0011 | Renault Clio | RENAULT:123, SINFA:456 | DFA0011.jpg |

## 🎨 Design Appliqué V3

- ✅ **Logo Dynamic Access** zoomé (logo.png ou texte)
- ✅ **Titre de catégorie** avec nom de la feuille Excel
- ✅ **Images des produits ZOOMÉES** (pas d'espace blanc)
- ✅ **Texte français** par défaut (pas d'arabe)
- ✅ **Direction LTR** pour le français
- ✅ **En-tête gris foncé** avec logo Dynamic
- ✅ **Bandeau rouge** avec texte français
- ✅ **6 produits par page** en grille 2x3
- ✅ **Références en orange**
- ✅ **Spécifications extraites** automatiquement
- ✅ **Numérotation professionnelle**

## 🔧 Extraction Intelligente

Le système extrait automatiquement ces références de la colonne "Correspondances" :
- **OEM** (RENAULT:)
- **SINFA** (SINFA:)
- **MECAFILTRE** (MECAFILTRE:)
- **KNECHT** (KNECHT:)
- **MISFAT** (MISFAT:)
- **MANN** (MANN:)
- **FILTRON** (FILTRON:)

## 📋 Fichiers Principaux V3

- **`generate-catalogue-dynamic-v3.js`** : Générateur V3 corrigé avec toutes les améliorations
- **`lance-dynamic-v3.js`** : Lanceur V3 simple et rapide
- **`GUIDE-DYNAMIC-V3.md`** : Guide détaillé d'utilisation V3

## 🌟 Avantages de la V3

### ✅ Images Parfaites
- **Images des produits** zoomées pour remplir le frame
- **Logo zoomé** pour remplir le frame
- **Aucun espace blanc** autour des images

### ✅ Titre de Catégorie Correct
- **Nom de la feuille Excel** utilisé automatiquement
- **Exemple** : "Filtre à Air Européennes"
- **Design moderne** et professionnel

### ✅ Texte Français
- **Supprimé l'arabe** par défaut
- **Direction LTR** pour le français
- **Bandeau en français** : "Filtre à air"

### ✅ Design Moderne
- **Titres de catégorie** avec style gris foncé
- **Motifs décoratifs** modernes
- **Layout professionnel** optimisé

## 🎉 Résultat Final

Un catalogue professionnel parfait avec le design **Dynamic Access V3** :
- En-tête avec logo zoomé
- Titres de catégorie basés sur vos feuilles Excel
- Images des produits zoomées (pas d'espace blanc)
- Texte français avec direction LTR
- Design moderne et professionnel
- Prêt pour l'impression

---

## 📖 Documentation Complète

Consultez `GUIDE-DYNAMIC-V3.md` pour des instructions détaillées et le dépannage.

## 🔄 Mise à Jour depuis V2

Si vous utilisez déjà la V2, la V3 corrige :
- ✅ Images des produits ZOOMÉES (pas d'espace blanc)
- ✅ Logo ZOOMÉ (pas d'espace blanc)
- ✅ Titre de catégorie correct (nom de la feuille Excel)
- ✅ Texte français par défaut (pas d'arabe)
- ✅ Direction LTR pour le français
- ✅ Bandeau avec "Filtre à air" en français

## 🎯 Utilisation Recommandée

**Utilisez la V3** pour un résultat parfait :
- Images zoomées sans espace blanc
- Logo zoomé parfaitement
- Titre de catégorie correct
- Texte français par défaut
- Design moderne et professionnel
