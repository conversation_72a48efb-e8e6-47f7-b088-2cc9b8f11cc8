# Catalogue Dynamic Access

## 🎯 Système de Génération de Catalogue

Ce projet génère automatiquement un catalogue InDesign professionnel avec le design **Dynamic Access** à partir de votre fichier Excel existant.

## 📁 Structure du Projet

```
Dynamic-Access/
├── excel/
│   └── catalogue.xlsx          # Votre fichier Excel avec les produits
├── images/                     # Dossier pour les images des produits
├── output/
│   └── generate_catalogue_dynamic.jsx  # Script InDesign généré
├── generate-catalogue-dynamic.js  # Générateur principal
├── lance-dynamic.js            # Lanceur simple
└── GUIDE-DYNAMIC.md            # Guide d'utilisation
```

## 🚀 Utilisation Rapide

1. **Préparer vos données** :
   - Placez votre fichier Excel dans `excel/catalogue.xlsx`
   - Placez vos images dans le dossier `images/`

2. **Générer le catalogue** :
   ```bash
   node lance-dynamic.js
   ```

3. **Créer dans InDesign** :
   - Ouvrir Adobe InDesign
   - Fichier > Scripts > Exécuter le script
   - Sélectionner : `output/generate_catalogue_dynamic.jsx`

## 📊 Format Excel Requis

| Colonne A | Colonne B | Colonne C | Colonne D |
|-----------|-----------|-----------|-----------|
| Référence | Applications | Correspondances | Images |
| ASZ1008 | RENAULT CLIO | RENAULT:123, SINFA:456 | DFA0011.jpg |

## 🎨 Design Appliqué

- ✅ **Logo Dynamic Access** (au lieu d'ASZ)
- ✅ **En-tête gris** avec logo Dynamic
- ✅ **Bandeau rouge** avec texte bilingue
- ✅ **6 produits par page** en grille 2x3
- ✅ **Références en orange**
- ✅ **Spécifications extraites** automatiquement
- ✅ **Images intégrées**
- ✅ **Numérotation des pages**

## 🔧 Extraction Automatique

Le système extrait automatiquement ces références de la colonne "Correspondances" :
- OEM (RENAULT:)
- SINFA (SINFA:)
- MECAFILTRE (MECAFILTRE:)
- KNECHT (KNECHT:)
- MISFAT (MISFAT:)
- MANN (MANN:)
- FILTRON (FILTRON:)

## 📋 Fichiers Principaux

- **`generate-catalogue-dynamic.js`** : Générateur principal avec logo Dynamic
- **`lance-dynamic.js`** : Lanceur simple et rapide
- **`GUIDE-DYNAMIC.md`** : Guide détaillé d'utilisation

## 🎉 Résultat

Un catalogue professionnel avec le design **Dynamic Access**, prêt à imprimer !

---

## 📖 Documentation Complète

Consultez `GUIDE-DYNAMIC.md` pour des instructions détaillées et le dépannage.
