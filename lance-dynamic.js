#!/usr/bin/env node

const path = require('path');
const fs = require('fs-extra');
const CatalogueDynamicGenerator = require('./generate-catalogue-dynamic.js');

console.log('🎯 Générateur Catalogue Dynamic');
console.log('===============================\n');

async function main() {
    try {
        // Vérifier que le fichier Excel existe
        const excelPath = path.join(__dirname, 'excel', 'catalogue.xlsx');

        if (!await fs.pathExists(excelPath)) {
            console.log('❌ Fichier excel/catalogue.xlsx non trouvé');
            console.log('💡 Veuillez placer votre fichier Excel dans le dossier excel/');
            process.exit(1);
        }

        console.log('✅ Fichier Excel trouvé : excel/catalogue.xlsx');

        // Vérifier le dossier images
        const imagesPath = path.join(__dirname, 'images');
        if (!await fs.pathExists(imagesPath)) {
            console.log('📁 Création du dossier images/');
            await fs.ensureDir(imagesPath);
        }

        console.log('\n🚀 Génération du catalogue Dynamic...');

        // Créer le générateur et lancer la génération
        const generator = new CatalogueDynamicGenerator();
        await generator.generateCatalogue();

        console.log('\n✅ Catalogue Dynamic généré avec succès !');
        console.log('\n📋 Prochaines étapes :');
        console.log('1. Ouvrir Adobe InDesign');
        console.log('2. Aller dans Fichier > Scripts > Exécuter le script');
        console.log('3. Sélectionner : output/generate_catalogue_dynamic.jsx');
        console.log('4. Le catalogue sera créé automatiquement avec le design Dynamic');

        console.log('\n📁 Fichier créé :');
        console.log('- output/generate_catalogue_dynamic.jsx');

        console.log('\n🎨 Design appliqué :');
        console.log('- En-tête avec logo Dynamic Access et bandeau rouge');
        console.log('- 6 produits par page en grille 2x3');
        console.log('- Références Dynamic en orange');
        console.log('- Spécifications extraites automatiquement');
        console.log('- Images intégrées');
        console.log('- Applications véhicules');
        console.log('- Numérotation des pages');

    } catch (error) {
        console.error('\n❌ Erreur :', error.message);
        console.log('\n🔧 Solutions possibles :');
        console.log('- Vérifiez que votre fichier Excel est bien formaté');
        console.log('- Vérifiez que les colonnes sont : Référence, Applications, Correspondances, Images');
        console.log('- Vérifiez les permissions des dossiers');
    }
}

// Lancer le script
main();
