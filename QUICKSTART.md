# 🚀 Démarrage Rapide - Catalogue Dynamic Access

## ⚡ Installation en 3 étapes

### 1. Installer Node.js
- Téléchargez depuis [nodejs.org](https://nodejs.org/)
- Installez la version LTS (recommandée)

### 2. Installer les dépendances
```bash
npm install
```

### 3. Configuration automatique
```bash
npm run setup
```

## 🎯 Utilisation immédiate

### 🚀 Avec votre fichier Excel existant
```bash
# 1. Convertir votre fichier Excel (structure simple)
npm run convert-excel-simple

# 2. Tester la conversion
npm run test-conversion

# 3. Générer le catalogue
npm run generate
```

### 📊 Structure Excel simplifiée recommandée
- **4 colonnes seulement** : Reference, Nom, Description, Equivalent, Image
- **Nom de la feuille = Catégorie automatique** (ex: "Filtre Air", "Gasoil", "Huile")
- **Une ligne = un produit** (plus de cellules fusionnées)
- **Facile à maintenir** et modifier

### Gérer vos produits
```bash
# Ajouter un produit
npm run add-product

# Modifier un produit  
npm run update-product

# Supprimer un produit
npm run delete-product

# Voir tous les produits
npm run list-products
```

### Générer le catalogue
```bash
npm run generate
```

## 📁 Structure créée automatiquement
```
Dynamic-Access/
├── excel/catalogue.csv      ← Vos produits ici
├── images/                  ← Vos images ici
├── templates/               ← Templates InDesign
├── output/                  ← Catalogue généré
└── config.js               ← Configuration
```

## 🔄 Workflow quotidien

1. **Modifiez** votre fichier `excel/catalogue.csv`
2. **Ajoutez** vos images dans `images/`
3. **Générez** le catalogue : `npm run generate`
4. **Ouvrez** dans InDesign pour finaliser

## 💡 Exemple d'ajout de produit

```bash
npm run add-product
```

Suivez les instructions :
- Référence : DFA1108
- Nom : Filtre à air nouvelle génération
- Catégorie : Filtre air
- Sous-catégorie : Européenne
- Description : Filtre haute performance...
- Équivalent : FAE008
- Image : filtre_air_008.jpg

## ⚠️ Points importants

- **Ordre Excel = Ordre catalogue** ✅
- **8 produits par page** automatiquement ✅
- **Design préservé** lors des mises à jour ✅
- **Catégories organisées** automatiquement ✅

## 🆘 Besoin d'aide ?

- **README.md** : Guide complet
- **config.js** : Personnalisation
- **npm run setup** : Réinitialisation

---

**🎉 Vous êtes prêt ! Commencez par `npm run setup`**
