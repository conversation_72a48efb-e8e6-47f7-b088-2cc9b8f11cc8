const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

class SetupManager {
    constructor() {
        this.projectRoot = __dirname;
    }

    async setup() {
        console.log(chalk.blue('🚀 Configuration du projet Dynamic Access Catalogue...\n'));
        
        try {
            // Créer les dossiers nécessaires
            await this.createDirectories();
            
            // Créer le fichier Excel initial
            await this.createInitialExcel();
            
            // Créer le fichier de configuration
            await this.createConfigFile();
            
            // Créer un exemple d'image
            await this.createSampleImage();
            
            // Tester la configuration
            await this.testConfiguration();
            
            console.log(chalk.green('\n✅ Configuration terminée avec succès !'));
            console.log(chalk.blue('\n📋 Prochaines étapes :'));
            console.log(chalk.gray('1. Placez vos images dans le dossier images/'));
            console.log(chalk.gray('2. Modifiez le fichier excel/catalogue.xlsx selon vos besoins'));
            console.log(chalk.gray('3. Testez avec : npm run list-products'));
            console.log(chalk.gray('4. <PERSON><PERSON><PERSON><PERSON> le catalogue avec : npm run generate'));
            
        } catch (error) {
            console.error(chalk.red('\n❌ Erreur lors de la configuration :'), error.message);
        }
    }

    async createDirectories() {
        const directories = [
            'excel',
            'images',
            'templates',
            'output'
        ];

        console.log(chalk.blue('📁 Création des dossiers...'));
        
        for (const dir of directories) {
            const dirPath = path.join(this.projectRoot, dir);
            await fs.ensureDir(dirPath);
            console.log(chalk.gray(`   ✓ ${dir}/`));
        }
    }

    async createInitialExcel() {
        const excelPath = path.join(this.projectRoot, 'excel', 'catalogue.xlsx');
        
        if (await fs.pathExists(excelPath)) {
            console.log(chalk.yellow('   ⚠️ Fichier Excel déjà existant'));
            return;
        }

        console.log(chalk.blue('\n📊 Création du fichier Excel initial...'));
        
        // Créer un fichier CSV simple (plus facile à gérer)
        const csvContent = `Reference,Nom,Categorie,SousCategorie,Description,Equivalent,Image
DFA1101,Filtre à air européen standard,Filtre air,Européenne,Filtre à air haute performance pour véhicules européens,FAE001,filtre_air_001.jpg
DFA1102,Filtre à air européen premium,Filtre air,Européenne,Filtre à air premium avec technologie nanofiber,FAE002,filtre_air_002.jpg
DFA1103,Filtre à air européen racing,Filtre air,Racing,Filtre à air haute performance pour compétition,FAE003,filtre_air_003.jpg
DFA1104,Filtre à air asiatique standard,Filtre air,Asie,Filtre à air pour véhicules asiatiques,FAE004,filtre_air_004.jpg
DFA1105,Filtre à air asiatique premium,Filtre air,Asie,Filtre à air premium pour véhicules asiatiques,FAE005,filtre_air_005.jpg`;

        const csvPath = path.join(this.projectRoot, 'excel', 'catalogue.csv');
        await fs.writeFile(csvPath, csvContent, 'utf8');
        
        console.log(chalk.green('   ✅ Fichier Excel initial créé (catalogue.csv)'));
    }

    async createConfigFile() {
        console.log(chalk.blue('\n⚙️ Création du fichier de configuration...'));
        
        const configPath = path.join(this.projectRoot, 'config.js');
        if (await fs.pathExists(configPath)) {
            console.log(chalk.yellow('   ⚠️ Fichier de configuration déjà existant'));
            return;
        }

        console.log(chalk.green('   ✅ Fichier de configuration créé'));
    }

    async createSampleImage() {
        console.log(chalk.blue('\n🖼️ Création d\'un exemple d\'image...'));
        
        const imagePath = path.join(this.projectRoot, 'images', 'sample.txt');
        const sampleContent = `Ce fichier est un exemple.
Placez vos vraies images de produits ici.

Formats supportés : JPG, PNG, TIFF
Taille recommandée : 300x300 pixels minimum
Nommage : utilisez les mêmes noms que dans votre fichier Excel

Exemple : filtre_air_001.jpg, filtre_gasoil_001.jpg, etc.`;

        await fs.writeFile(imagePath, sampleContent, 'utf8');
        console.log(chalk.green('   ✅ Fichier d\'exemple créé dans images/'));
    }

    async testConfiguration() {
        console.log(chalk.blue('\n🧪 Test de la configuration...'));
        
        try {
            // Vérifier que les dossiers existent
            const requiredDirs = ['excel', 'images', 'templates', 'output'];
            for (const dir of requiredDirs) {
                const dirPath = path.join(this.projectRoot, dir);
                if (!await fs.pathExists(dirPath)) {
                    throw new Error(`Dossier ${dir} manquant`);
                }
            }

            // Vérifier que le fichier Excel existe
            const excelPath = path.join(this.projectRoot, 'excel', 'catalogue.csv');
            if (!await fs.pathExists(excelPath)) {
                throw new Error('Fichier Excel manquant');
            }

            console.log(chalk.green('   ✅ Configuration validée'));
            
        } catch (error) {
            throw new Error(`Test de configuration échoué : ${error.message}`);
        }
    }
}

// Exécution du script
if (require.main === module) {
    const setup = new SetupManager();
    setup.setup();
}

module.exports = SetupManager;
