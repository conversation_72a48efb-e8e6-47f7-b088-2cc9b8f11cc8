// Configuration du catalogue Dynamic Access
module.exports = {
    // Informations générales
    company: {
        name: "Dynamic Access",
        logo: "logo.png",
        website: "www.dynamic-access.com"
    },
    
    // Paramètres du catalogue
    catalogue: {
        title: "Catalogue Filtres Dynamic Access",
        subtitle: "Qualité et Performance",
        version: "2024",
        language: "fr"
    },
    
    // Mise en page
    layout: {
        pageSize: "A4",
        orientation: "portrait",
        productsPerPage: 8,
        margins: {
            top: 20,
            bottom: 20,
            left: 20,
            right: 20
        },
        spacing: {
            betweenProducts: 10,
            betweenRows: 15,
            betweenColumns: 15
        }
    },
    
    // Styles et couleurs
    styles: {
        colors: {
            primary: "#2C3E50",      // Bleu foncé
            secondary: "#34495E",     // Bleu gris
            accent: "#E74C3C",        // Rouge accent
            text: "#2C3E50",          // Texte principal
            lightText: "#7F8C8D",     // Texte secondaire
            background: "#FFFFFF"      // Fond blanc
        },
        fonts: {
            title: "Arial Bold",
            subtitle: "Arial",
            body: "Arial",
            product: "Arial Bold"
        },
        sizes: {
            title: 18,
            subtitle: 14,
            categoryTitle: 16,
            productName: 12,
            productRef: 10,
            description: 10
        }
    },
    
    // Catégories prédéfinies
    categories: [
        "Filtre air",
        "Gasoil", 
        "Huile",
        "Habitacle",
        "Poids lourds"
    ],
    
    // Sous-catégories par défaut
    subCategories: {
        "Filtre air": ["Européenne", "Asie", "Amérique", "Racing"],
        "Gasoil": ["Européenne", "Asie", "Amérique", "Haute performance"],
        "Huile": ["Européenne", "Asie", "Amérique", "Synthetique"],
        "Habitacle": ["Standard", "Premium", "Anti-allergène", "Charbon actif"],
        "Poids lourds": ["Camion", "Bus", "Engin de chantier", "Agricole"]
    },
    
    // Paramètres des images
    images: {
        maxWidth: 80,
        maxHeight: 80,
        quality: "high",
        formats: ["jpg", "png", "tiff"],
        defaultImage: "no-image.jpg"
    },
    
    // Paramètres de sortie
    output: {
        format: "indesign", // ou "pdf", "html"
        includeTableOfContents: true,
        includeCover: true,
        includePageNumbers: true,
        compression: "high"
    },
    
    // Paramètres de génération
    generation: {
        autoSave: true,
        backupBeforeGeneration: true,
        validateData: true,
        showProgress: true
    }
};
