// Script InDesign pour Catalogue Dynamic V2
// Généré automatiquement pour votre structure Excel existante
// Support arabe avec direction RTL

// Fonction pour créer un nouveau document
function createDocument() {
    var doc = app.documents.add();
    doc.documentPreferences.pageWidth = "210mm";
    doc.documentPreferences.pageHeight = "297mm";
    doc.documentPreferences.facingPages = false;
    doc.documentPreferences.pageOrientation = PageOrientation.PORTRAIT;
    
    // Créer les couleurs nécessaires
    createColors(doc);
    
    return doc;
}

// Fonction pour créer les couleurs
function createColors(doc) {
    // Couleur rouge pour les bandeaux
    try {
        var redColor = doc.colors.itemByName("Rouge");
        if (!redColor || redColor.isValid === false) {
            redColor = doc.colors.add();
            redColor.name = "Rouge";
            redColor.colorValue = [0, 100, 100, 0]; // Rouge
        }
    } catch (e) {
        redColor = doc.colors.add();
        redColor.name = "Rouge";
        redColor.colorValue = [0, 100, 100, 0];
    }
    
    // Couleur orange pour les références
    try {
        var orangeColor = doc.colors.itemByName("Orange");
        if (!orangeColor || orangeColor.isValid === false) {
            orangeColor = doc.colors.add();
            orangeColor.name = "Orange";
            orangeColor.colorValue = [0, 50, 100, 0]; // Orange
        }
    } catch (e) {
        orangeColor = doc.colors.add();
        orangeColor.name = "Orange";
        orangeColor.colorValue = [0, 50, 100, 0];
    }
    
    // Couleur grise pour le logo
    try {
        var grayColor = doc.colors.itemByName("Gris");
        if (!grayColor || grayColor.isValid === false) {
            grayColor = doc.colors.add();
            grayColor.name = "Gris";
            grayColor.colorValue = [0, 0, 0, 50]; // Gris 50%
        }
    } catch (e) {
        grayColor = doc.colors.add();
        grayColor.name = "Gris";
        grayColor.colorValue = [0, 0, 0, 50];
    }
    
    // Couleur gris foncé pour les titres de catégorie
    try {
        var darkGrayColor = doc.colors.itemByName("GrisFonce");
        if (!darkGrayColor || darkGrayColor.isValid === false) {
            darkGrayColor = doc.colors.add();
            darkGrayColor.name = "GrisFonce";
            darkGrayColor.colorValue = [0, 0, 0, 80]; // Gris 80%
        }
    } catch (e) {
        darkGrayColor = doc.colors.add();
        darkGrayColor.name = "GrisFonce";
        darkGrayColor.colorValue = [0, 0, 0, 80];
    }
}

// Fonction pour créer les styles
function createStyles(doc) {
    // Style pour le titre principal
    var titleStyle = doc.paragraphStyles.add();
    titleStyle.name = "TitrePrincipal";
    titleStyle.appliedFont = app.fonts.itemByName("Arial");
    titleStyle.fontStyle = "Bold";
    titleStyle.pointSize = 24;
    titleStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les références Dynamic
    var refStyle = doc.paragraphStyles.add();
    refStyle.name = "ReferenceDynamic";
    refStyle.appliedFont = app.fonts.itemByName("Arial");
    refStyle.fontStyle = "Bold";
    refStyle.pointSize = 14;
    refStyle.fillColor = doc.colors.itemByName("Orange");
    
    // Style pour les spécifications
    var specStyle = doc.paragraphStyles.add();
    specStyle.name = "Specifications";
    specStyle.appliedFont = app.fonts.itemByName("Arial");
    specStyle.pointSize = 8;
    specStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les applications
    var appStyle = doc.paragraphStyles.add();
    appStyle.name = "Applications";
    appStyle.appliedFont = app.fonts.itemByName("Arial");
    appStyle.pointSize = 7;
    appStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour le bandeau rouge
    var bandeauStyle = doc.paragraphStyles.add();
    bandeauStyle.name = "BandeauRouge";
    bandeauStyle.appliedFont = app.fonts.itemByName("Arial");
    bandeauStyle.fontStyle = "Bold";
    bandeauStyle.pointSize = 12;
    bandeauStyle.fillColor = doc.colors.itemByName("Paper");
    
    // Style pour les titres de catégorie (gris foncé, moderne)
    var categoryStyle = doc.paragraphStyles.add();
    categoryStyle.name = "TitreCategorie";
    categoryStyle.appliedFont = app.fonts.itemByName("Arial");
    categoryStyle.fontStyle = "Bold";
    categoryStyle.pointSize = 18;
    categoryStyle.fillColor = doc.colors.itemByName("GrisFonce");
    
    return {
        titleStyle: titleStyle,
        refStyle: refStyle,
        specStyle: specStyle,
        appStyle: appStyle,
        bandeauStyle: bandeauStyle,
        categoryStyle: categoryStyle
    };
}

// Fonction pour créer l'en-tête de page
function createPageHeader(page, styles) {
    // Logo Dynamic (zone grise)
    var logoFrame = page.rectangles.add();
    logoFrame.geometricBounds = [10, 10, 30, 80];
    logoFrame.fillColor = doc.colors.itemByName("Gris");
    
    // Image logo.png
    try {
        var logoPath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/logo.png";
        var logoFile = new File(logoPath);
        if (logoFile.exists) {
            var logoImageFrame = page.rectangles.add();
            logoImageFrame.geometricBounds = [12, 15, 28, 75];
            logoImageFrame.place(File(logoPath));
            logoImageFrame.fit(FittingOptions.FILL_FRAME_PROPORTIONALLY);
            logoImageFrame.fit(FittingOptions.CENTER_CONTENT);
        } else {
            // Texte Dynamic Access si l'image n'existe pas
            var logoText = page.textFrames.add();
            logoText.geometricBounds = [12, 15, 28, 75];
            logoText.contents = "Dynamic\nAccess";
            logoText.paragraphs[0].appliedParagraphStyle = styles.titleStyle;
            logoText.paragraphs[0].justification = Justification.CENTER_ALIGN;
            logoText.paragraphs[0].fillColor = doc.colors.itemByName("Paper");
        }
    } catch (e) {
        // Texte Dynamic Access en cas d'erreur
        var logoText = page.textFrames.add();
        logoText.geometricBounds = [12, 15, 28, 75];
        logoText.contents = "Dynamic\nAccess";
        logoText.paragraphs[0].appliedParagraphStyle = styles.titleStyle;
        logoText.paragraphs[0].justification = Justification.CENTER_ALIGN;
        logoText.paragraphs[0].fillColor = doc.colors.itemByName("Paper");
    }
    
    // Bandeau rouge
    var bandeauFrame = page.rectangles.add();
    bandeauFrame.geometricBounds = [10, 150, 30, 200];
    bandeauFrame.fillColor = doc.colors.itemByName("Rouge");
    
    // Texte du bandeau
    var bandeauText = page.textFrames.add();
    bandeauText.geometricBounds = [12, 155, 28, 195];
    bandeauText.contents = "RENAULT - DACIA - CITROEN - PEUGEOT\nمصفاة هواء / Filtre à air";
    bandeauText.paragraphs[0].appliedParagraphStyle = styles.bandeauStyle;
    bandeauText.paragraphs[0].justification = Justification.CENTER_ALIGN;
}

// Fonction pour créer un titre de catégorie moderne
function createCategoryTitle(page, categoryName, y, styles) {
    // Titre de la catégorie
    var categoryTitle = page.textFrames.add();
    categoryTitle.geometricBounds = [y, 10, y + 15, 200];
    categoryTitle.contents = categoryName;
    categoryTitle.paragraphs[0].appliedParagraphStyle = styles.categoryStyle;
    categoryTitle.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Ligne décorative moderne sous le titre
    var line = page.graphicLines.add();
    line.strokeColor = doc.colors.itemByName("Rouge");
    line.strokeWeight = 2;
    line.geometricBounds = [y + 16, 20, y + 16, 190];
    
    // Motifs décoratifs modernes
    var motif1 = page.rectangles.add();
    motif1.geometricBounds = [y + 17, 15, y + 19, 17];
    motif1.fillColor = doc.colors.itemByName("Rouge");
    
    var motif2 = page.rectangles.add();
    motif2.geometricBounds = [y + 17, 193, y + 19, 195];
    motif2.fillColor = doc.colors.itemByName("Rouge");
    
    var motif3 = page.rectangles.add();
    motif3.geometricBounds = [y + 17, 102, y + 19, 104];
    motif3.fillColor = doc.colors.itemByName("Orange");
}

// Fonction pour créer un produit individuel
function createProduct(page, product, x, y, width, height, styles) {
    // Cadre du produit
    var productFrame = page.rectangles.add();
    productFrame.geometricBounds = [y, x, y + height, x + width];
    productFrame.strokeColor = doc.colors.itemByName("Black");
    productFrame.strokeWeight = 0.5;
    
    // Référence Dynamic (en haut)
    var refFrame = page.textFrames.add();
    refFrame.geometricBounds = [y + 2, x + 2, y + 12, x + width - 2];
    refFrame.contents = product.Référence;
    refFrame.paragraphs[0].appliedParagraphStyle = styles.refStyle;
    
    // Spécifications (à gauche)
    var specFrame = page.textFrames.add();
    specFrame.geometricBounds = [y + 15, x + 2, y + 45, x + width/2 - 2];
    var specContent = "OEM: " + (product.OEM || '') + "\n";
    specContent += "SINFA: " + (product.SINFA || '') + "\n";
    specContent += "MECAFILTRE: " + (product.MECAFILTRE || '') + "\n";
    specContent += "KNECHT: " + (product.KNECHT || '') + "\n";
    specContent += "MISFAT: " + (product.MISFAT || '') + "\n";
    specContent += "MANN: " + (product.MANN || '') + "\n";
    specContent += "FILTRON: " + (product.FILTRON || '');
    specFrame.contents = specContent;
    specFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    
    // Image du produit (au centre)
    if (product.Images) {
        try {
            var imagePath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/" + product.Images;
            var imageFile = new File(imagePath);
            if (imageFile.exists) {
                var imageFrame = page.rectangles.add();
                imageFrame.geometricBounds = [y + 15, x + width/2 + 2, y + 45, x + width - 2];
                imageFrame.place(File(imagePath));
                imageFrame.fit(FittingOptions.FILL_FRAME_PROPORTIONALLY);
                imageFrame.fit(FittingOptions.CENTER_CONTENT);
            }
        } catch (e) {
            // Si l'image n'existe pas, on continue
        }
    }
    
    // Applications (en bas) - Direction RTL pour l'arabe
    var appFrame = page.textFrames.add();
    appFrame.geometricBounds = [y + 50, x + 2, y + height - 2, x + width - 2];
    appFrame.contents = product.Applications || '';
    appFrame.paragraphs[0].appliedParagraphStyle = styles.appStyle;
    
    // Appliquer la direction RTL pour le texte arabe
    try {
        appFrame.paragraphs[0].justification = Justification.RIGHT_ALIGN;
        appFrame.paragraphs[0].paragraphDirection = ParagraphDirection.RIGHT_TO_LEFT;
    } catch (e) {
        // Fallback si la propriété n'est pas supportée
    }
}

// Fonction pour créer une page de produits
function createProductPage(doc, products, pageNumber, styles, categoryName) {
    var page = doc.pages.add();
    
    // Créer l'en-tête
    createPageHeader(page, styles);
    
    // Créer le titre de catégorie moderne
    createCategoryTitle(page, categoryName, 35, styles);
    
    // Dimensions pour 6 produits (2x3)
    var pageWidth = 210; // mm
    var pageHeight = 297; // mm
    var margin = 10; // mm
    var productWidth = (pageWidth - 3 * margin) / 2; // 2 colonnes
    var productHeight = (pageHeight - 4 * margin - 60) / 3; // 3 rangées, moins l'en-tête et le titre
    
    // Position de départ (après l'en-tête et le titre)
    var startY = 60;
    var startX = margin;
    
    // Créer les 6 produits
    for (var i = 0; i < Math.min(6, products.length); i++) {
        var row = Math.floor(i / 2);
        var col = i % 2;
        
        var x = startX + col * (productWidth + margin);
        var y = startY + row * (productHeight + margin);
        
        createProduct(page, products[i], x, y, productWidth, productHeight, styles);
    }
    
    // Numéro de page
    var pageNumFrame = page.textFrames.add();
    pageNumFrame.geometricBounds = [pageHeight - 15, pageWidth/2 - 10, pageHeight - 10, pageWidth/2 + 10];
    pageNumFrame.contents = "-" + pageNumber + "-";
    pageNumFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    pageNumFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Lignes décoratives autour du numéro de page
    var line1 = page.graphicLines.add();
    line1.strokeColor = doc.colors.itemByName("Rouge");
    line1.strokeWeight = 1;
    line1.geometricBounds = [pageHeight - 12, pageWidth/2 - 20, pageHeight - 12, pageWidth/2 - 15];
    
    var line2 = page.graphicLines.add();
    line2.strokeColor = doc.colors.itemByName("Rouge");
    line2.strokeWeight = 1;
    line2.geometricBounds = [pageHeight - 12, pageWidth/2 + 15, pageHeight - 12, pageWidth/2 + 20];
}

// Début du script principal
var doc = createDocument();
var styles = createStyles(doc);

// Données des produits par catégorie

// Catégorie: Filtre à Air Européennes
var products0 = [
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
];
createProductPage(doc, products0.slice(0, 6), 1, styles, "Filtre à Air Européennes");
createProductPage(doc, products0.slice(6, 12), 2, styles, "Filtre à Air Européennes");



alert("Catalogue Dynamic V2 généré avec succès !");
