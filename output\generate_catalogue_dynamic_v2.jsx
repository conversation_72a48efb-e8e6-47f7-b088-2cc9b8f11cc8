// Script InDesign pour Catalogue Dynamic V2
// Généré automatiquement pour votre structure Excel existante
// Support français avec direction LTR - Design comme image de référence

// Fonction pour créer un nouveau document
function createDocument() {
    var doc = app.documents.add();
    doc.documentPreferences.pageWidth = "210mm";
    doc.documentPreferences.pageHeight = "297mm";
    doc.documentPreferences.facingPages = false;
    doc.documentPreferences.pageOrientation = PageOrientation.PORTRAIT;

    // Créer les couleurs nécessaires
    createColors(doc);

    return doc;
}

// Fonction pour créer les couleurs
function createColors(doc) {
    // Couleur rouge pour les bandeaux
    try {
        var redColor = doc.colors.itemByName("Rouge");
        if (!redColor || redColor.isValid === false) {
            redColor = doc.colors.add();
            redColor.name = "Rouge";
            redColor.colorValue = [0, 100, 100, 0]; // Rouge
        }
    } catch (e) {
        redColor = doc.colors.add();
        redColor.name = "Rouge";
        redColor.colorValue = [0, 100, 100, 0];
    }
    
    // Couleur orange pour les références
    try {
        var orangeColor = doc.colors.itemByName("Orange");
        if (!orangeColor || orangeColor.isValid === false) {
            orangeColor = doc.colors.add();
            orangeColor.name = "Orange";
            orangeColor.colorValue = [0, 50, 100, 0]; // Orange
        }
    } catch (e) {
        orangeColor = doc.colors.add();
        orangeColor.name = "Orange";
        orangeColor.colorValue = [0, 50, 100, 0];
    }
    
    // Couleur grise pour le logo
    try {
        var grayColor = doc.colors.itemByName("Gris");
        if (!grayColor || grayColor.isValid === false) {
            grayColor = doc.colors.add();
            grayColor.name = "Gris";
            grayColor.colorValue = [0, 0, 0, 50]; // Gris 50%
        }
    } catch (e) {
        grayColor = doc.colors.add();
        grayColor.name = "Gris";
        grayColor.colorValue = [0, 0, 0, 50];
    }
    
    // Couleur gris foncé pour les titres de catégorie
    try {
        var darkGrayColor = doc.colors.itemByName("GrisFonce");
        if (!darkGrayColor || darkGrayColor.isValid === false) {
            darkGrayColor = doc.colors.add();
            darkGrayColor.name = "GrisFonce";
            darkGrayColor.colorValue = [0, 0, 0, 80]; // Gris 80%
        }
    } catch (e) {
        darkGrayColor = doc.colors.add();
        darkGrayColor.name = "GrisFonce";
        darkGrayColor.colorValue = [0, 0, 0, 80];
    }
}

// Fonction pour créer les styles
function createStyles(doc) {
    // Style pour le titre principal
    var titleStyle = doc.paragraphStyles.add();
    titleStyle.name = "TitrePrincipal";
    titleStyle.appliedFont = app.fonts.itemByName("Arial");
    titleStyle.fontStyle = "Bold";
    titleStyle.pointSize = 24;
    titleStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour les références Dynamic
    var refStyle = doc.paragraphStyles.add();
    refStyle.name = "ReferenceDynamic";
    refStyle.appliedFont = app.fonts.itemByName("Arial");
    refStyle.fontStyle = "Bold";
    refStyle.pointSize = 14;
    refStyle.fillColor = doc.colors.itemByName("Orange");
    
    // Style pour les spécifications
    var specStyle = doc.paragraphStyles.add();
    specStyle.name = "Specifications";
    specStyle.appliedFont = app.fonts.itemByName("Arial");
    specStyle.pointSize = 8;
    specStyle.fillColor = doc.colors.itemByName("Black");
    
    // Style pour le titre "Application" (noir, gras)
    var appTitleStyle = doc.paragraphStyles.add();
    appTitleStyle.name = "ApplicationTitle";
    appTitleStyle.appliedFont = app.fonts.itemByName("Arial");
    appTitleStyle.fontStyle = "Bold";
    appTitleStyle.pointSize = 9;
    appTitleStyle.fillColor = doc.colors.itemByName("Black");

    // Style pour les applications
    var appStyle = doc.paragraphStyles.add();
    appStyle.name = "Applications";
    appStyle.appliedFont = app.fonts.itemByName("Arial");
    appStyle.pointSize = 8;
    appStyle.fillColor = doc.colors.itemByName("Black");

    // Style pour le bandeau orange
    var bandeauStyle = doc.paragraphStyles.add();
    bandeauStyle.name = "BandeauOrange";
    bandeauStyle.appliedFont = app.fonts.itemByName("Arial");
    bandeauStyle.fontStyle = "Bold";
    bandeauStyle.pointSize = 14;
    bandeauStyle.fillColor = doc.colors.itemByName("Paper");

    // Style pour les titres de catégorie (gris foncé, moderne)
    var categoryStyle = doc.paragraphStyles.add();
    categoryStyle.name = "TitreCategorie";
    categoryStyle.appliedFont = app.fonts.itemByName("Arial");
    categoryStyle.fontStyle = "Bold";
    categoryStyle.pointSize = 18;
    categoryStyle.fillColor = doc.colors.itemByName("GrisFonce");

    return {
        titleStyle: titleStyle,
        refStyle: refStyle,
        specStyle: specStyle,
        appTitleStyle: appTitleStyle,
        appStyle: appStyle,
        bandeauStyle: bandeauStyle,
        categoryStyle: categoryStyle
    };
}

// Fonction pour créer l'en-tête de page selon le design de référence
function createPageHeader(page, styles) {
    // Logo Dynamic Access (en haut à gauche) - seulement l'image, pas de texte ni bordure
    try {
        var logoPath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/logo.png";
        var logoFile = new File(logoPath);
        if (logoFile.exists) {
            var logoImageFrame = page.rectangles.add();
            logoImageFrame.geometricBounds = [5, 10, 25, 80];
            logoImageFrame.strokeWeight = 0; // Pas de bordure
            logoImageFrame.place(File(logoPath));
            logoImageFrame.fit(FittingOptions.FILL_FRAME_PROPORTIONALLY);
            logoImageFrame.fit(FittingOptions.CENTER_CONTENT);
        }
        // Pas de texte de fallback - seulement l'image
    } catch (e) {
        // Pas de texte de fallback en cas d'erreur
    }

    // Bandeau orange "Filtre à Air Européennes" (en haut à droite)
    var bandeauFrame = page.rectangles.add();
    bandeauFrame.geometricBounds = [5, 120, 25, 200];
    bandeauFrame.fillColor = doc.colors.itemByName("Orange");

    // Texte du bandeau
    var bandeauText = page.textFrames.add();
    bandeauText.geometricBounds = [8, 125, 22, 195];
    bandeauText.contents = "Filtre à Air Européennes";
    bandeauText.paragraphs[0].appliedParagraphStyle = styles.bandeauStyle;
    bandeauText.paragraphs[0].justification = Justification.CENTER_ALIGN;
}



// Fonction pour créer un produit individuel selon le design de référence
function createProduct(page, product, x, y, width, height, styles) {
    // Cadre principal du produit sans bordure (comme dans le design)
    var productFrame = page.rectangles.add();
    productFrame.geometricBounds = [y, x, y + height, x + width];
    productFrame.strokeWeight = 0; // Pas de bordure
    productFrame.fillColor = doc.colors.itemByName("Paper");

    // Référence du produit (orange, en haut à gauche)
    var refFrame = page.textFrames.add();
    refFrame.geometricBounds = [y + 2, x + 2, y + 12, x + 60];
    refFrame.contents = product.Référence || '';
    refFrame.paragraphs[0].appliedParagraphStyle = styles.refStyle;
    refFrame.paragraphs[0].justification = Justification.LEFT_ALIGN;

    // Ligne de séparation sous la référence
    var separatorLine = page.graphicLines.add();
    separatorLine.strokeColor = doc.colors.itemByName("Orange");
    separatorLine.strokeWeight = 1;
    separatorLine.paths[0].pathPoints[0].anchor = [x + 2, y + 14];
    separatorLine.paths[0].pathPoints[1].anchor = [x + width - 2, y + 14];

    // Image du produit (à gauche, dimensions comme dans le design précédent)
    if (product.Images) {
        try {
            var imagePath = "C:/Works/ASZ/Catalogues/Dynamic-Access/images/" + product.Images;
            var imageFile = new File(imagePath);
            if (imageFile.exists) {
                var imageFrame = page.rectangles.add();
                imageFrame.geometricBounds = [y + 18, x + 5, y + 50, x + 40]; // Dimensions ajustées
                imageFrame.strokeWeight = 0; // Pas de bordure
                imageFrame.place(File(imagePath));
                imageFrame.fit(FittingOptions.FILL_FRAME_PROPORTIONALLY);
                imageFrame.fit(FittingOptions.CENTER_CONTENT);
            }
        } catch (e) {
            // Si l'image n'existe pas, on continue
        }
    }

    // Spécifications techniques (à droite de l'image, format colonnes avec retours à la ligne)
    var specFrame = page.textFrames.add();
    specFrame.geometricBounds = [y + 18, x + 45, y + 45, x + width - 2];
    var specText = '';

    // Parser la colonne Correspondances qui contient "RENAULT: 7701034705 | ASZ: ASZ1011 | ..."
    if (product.Correspondances) {
        var correspondances = product.Correspondances.split(' | ');
        for (var i = 0; i < correspondances.length; i++) {
            var corr = correspondances[i].trim();
            if (corr && corr.indexOf(':') > -1) {
                var parts = corr.split(':');
                var marque = parts[0].trim();
                var reference = parts[1].trim();
                specText += marque + '\\t\\t' + reference + '\\r';
            }
        }
    }

    specFrame.contents = specText;
    specFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    specFrame.paragraphs[0].justification = Justification.LEFT_ALIGN;

    // Forcer la langue en français/anglais
    try {
        specFrame.paragraphs[0].appliedLanguage = app.languagesWithVendors.itemByName("French");
    } catch (e) {
        try {
            specFrame.paragraphs[0].appliedLanguage = app.languagesWithVendors.itemByName("English");
        } catch (e2) {
            // Fallback si les langues ne sont pas disponibles
        }
    }

    // Titre "Application" (en gras)
    var appTitleFrame = page.textFrames.add();
    appTitleFrame.geometricBounds = [y + 48, x + 2, y + 55, x + 50];
    appTitleFrame.contents = 'Application';
    appTitleFrame.paragraphs[0].appliedParagraphStyle = styles.appTitleStyle;
    appTitleFrame.paragraphs[0].justification = Justification.LEFT_ALIGN;

    // Texte des applications (en bas, direction LTR avec langue française)
    var appFrame = page.textFrames.add();
    appFrame.geometricBounds = [y + 57, x + 2, y + height - 2, x + width - 2];
    appFrame.contents = product.Applications || '';
    appFrame.paragraphs[0].appliedParagraphStyle = styles.appStyle;
    appFrame.paragraphs[0].justification = Justification.LEFT_ALIGN;
    appFrame.paragraphs[0].paragraphDirection = ParagraphDirection.LEFT_TO_RIGHT;

    // Forcer la langue en français/anglais pour les applications
    try {
        appFrame.paragraphs[0].appliedLanguage = app.languagesWithVendors.itemByName("French");
    } catch (e) {
        try {
            appFrame.paragraphs[0].appliedLanguage = app.languagesWithVendors.itemByName("English");
        } catch (e2) {
            // Fallback si les langues ne sont pas disponibles
        }
    }
}

// Fonction pour créer une page de produits
function createProductPage(doc, products, pageNumber, styles, categoryName) {
    var page = doc.pages.add();
    
    // Créer l'en-tête
    createPageHeader(page, styles);

    // Dimensions pour 4 produits (2x2) comme dans l'image de référence
    var pageWidth = 210; // mm
    var pageHeight = 297; // mm
    var margin = 10; // mm
    var productWidth = (pageWidth - 3 * margin) / 2; // 2 colonnes
    var productHeight = (pageHeight - 3 * margin - 60) / 2; // 2 rangées, moins l'en-tête

    // Position de départ (après l'en-tête)
    var startY = 35;
    var startX = margin;

    // Créer les 4 produits (2 colonnes x 2 rangées)
    for (var i = 0; i < Math.min(4, products.length); i++) {
        var row = Math.floor(i / 2);
        var col = i % 2;

        var x = startX + col * (productWidth + margin);
        var y = startY + row * (productHeight + margin);

        createProduct(page, products[i], x, y, productWidth, productHeight, styles);
    }
    
    // Numéro de page
    var pageNumFrame = page.textFrames.add();
    pageNumFrame.geometricBounds = [pageHeight - 15, pageWidth/2 - 10, pageHeight - 10, pageWidth/2 + 10];
    pageNumFrame.contents = "-" + pageNumber + "-";
    pageNumFrame.paragraphs[0].appliedParagraphStyle = styles.specStyle;
    pageNumFrame.paragraphs[0].justification = Justification.CENTER_ALIGN;
    
    // Lignes décoratives autour du numéro de page
    var line1 = page.graphicLines.add();
    line1.strokeColor = doc.colors.itemByName("Rouge");
    line1.strokeWeight = 1;
    line1.geometricBounds = [pageHeight - 12, pageWidth/2 - 20, pageHeight - 12, pageWidth/2 - 15];
    
    var line2 = page.graphicLines.add();
    line2.strokeColor = doc.colors.itemByName("Rouge");
    line2.strokeWeight = 1;
    line2.geometricBounds = [pageHeight - 12, pageWidth/2 + 15, pageHeight - 12, pageWidth/2 + 20];
}

// Début du script principal
var doc = createDocument();
var styles = createStyles(doc);

// Données des produits par catégorie

// Catégorie: Filtre à Air Européennes
var products0 = [
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0011",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0012",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
  {
    Référence: "DFA0014",
    OEM: "7701034705",
    SINFA: "22877",
    MECAFILTRE: "EL3413",
    KNECHT: "LX329",
    MISFAT: "R194",
    MANN: "C16113",
    FILTRON: "AR282",
    Applications: "Renault Clio 1.9 D (64 HP) | Clio 1.8i RT, Initiale (90 HP)| Clio 1.7 RT, Baccara (92 HP) | Clio 1.8i RT, RSI (95/110 HP) | Express 1.9 D (65 HP)",
    Images: "DFA0011.jpg"
  },
];

// Créer les pages avec 4 produits chacune
var pageNumber = 1;
for (var i = 0; i < products0.length; i += 4) {
    var pageProducts = products0.slice(i, i + 4);
    createProductPage(doc, pageProducts, pageNumber, styles, "Filtre à Air Européennes");
    pageNumber++;
}

alert("Catalogue Dynamic V2 généré avec succès !");
