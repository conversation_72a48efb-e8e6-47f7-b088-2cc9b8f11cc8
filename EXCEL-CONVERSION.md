# 🔄 Conversion de votre fichier Excel - Guide étape par étape

## 📋 Votre fichier Excel actuel

Votre fichier Excel contient :
- **Onglets** : F, Air EU, F,Air AS, Gasoil EU, Gasoil AS, Huile EU, Huile AS, F, AIR PL, HABITACLE
- **Colonnes** : Marque, Référence, Applications, Correspondances, Images
- **Structure** : Cellules fusionnées pour une meilleure lisibilité

## 🚀 Conversion automatique

### Étape 1 : Préparer votre fichier
1. **Renommez** votre fichier Excel en `catalogue.xlsx`
2. **Placez-le** dans le dossier `excel/`
3. **Vérifiez** que tous vos onglets sont présents

### Étape 2 : Lancer la conversion
```bash
npm run convert-excel
```

### Étape 3 : Vérifier le résultat
La conversion crée automatiquement :
- `excel/catalogue_converted.xlsx` - Fichier converti
- `output/catalogue_config.json` - Configuration du catalogue

## 🔍 Ce que fait la conversion

### Mapping automatique des onglets
| Votre onglet | → | Catégorie | Sous-catégorie |
|---------------|---|------------|----------------|
| F, Air EU | → | Filtre air | Européenne |
| F,Air AS | → | Filtre air | Asie |
| Gasoil EU | → | Gasoil | Européenne |
| Gasoil AS | → | Gasoil | Asie |
| Huile EU | → | Huile | Européenne |
| Huile AS | → | Huile | Asie |
| F, AIR PL | → | Filtre air | Poids lourds |
| HABITACLE | → | Habitacle | Standard |

### Conversion des colonnes
| Votre colonne | → | Colonne système | Exemple |
|---------------|---|-----------------|---------|
| Référence | → | Reference | DFA0011 |
| Marque + Applications | → | Nom | Filtre RENAULT - Clio 1.9 D |
| Applications | → | Description | Clio 1.9 D (64 HP), Clio 1.8i RT... |
| Correspondances | → | Equivalent | RENAULT, ASZ, SINFA... |
| Images | → | Image | DFA0011 |

## ✅ Avantages de la conversion

1. **Préserve l'ordre** : Vos produits gardent leur position exacte
2. **Respecte la structure** : Toutes vos données sont conservées
3. **Automatique** : Aucune saisie manuelle requise
4. **Compatible** : Fonctionne avec le système de catalogue

## 📊 Exemple de conversion

### Avant (votre fichier)
```
Onglet: F, Air EU
Marque: RENAULT
Référence: DFA0011
Applications: Clio 1.9 D (64 HP), Clio 1.8i RT...
Correspondances: RENAULT, ASZ, SINFA, MECAFILTRE...
Images: DFA0011
```

### Après (fichier converti)
```
Reference: DFA0011
Nom: Filtre RENAULT - Clio 1.9 D
Categorie: Filtre air
SousCategorie: Européenne
Description: Clio 1.9 D (64 HP), Clio 1.8i RT...
Equivalent: RENAULT
Image: DFA0011
```

## 🔄 Workflow complet

1. **Placez** votre fichier `catalogue.xlsx` dans `excel/`
2. **Convertissez** : `npm run convert-excel`
3. **Vérifiez** le fichier converti
4. **Générez** le catalogue : `npm run generate`
5. **Personnalisez** dans InDesign

## ⚠️ Notes importantes

- **Sauvegarde** : Gardez une copie de votre fichier original
- **Images** : Assurez-vous que les noms correspondent aux fichiers
- **Ordre** : La conversion respecte l'ordre exact de vos lignes
- **Données** : Toutes vos informations sont préservées

## 🆘 En cas de problème

### Erreur "Fichier Excel source non trouvé"
- Vérifiez que `excel/catalogue.xlsx` existe
- Vérifiez le nom exact du fichier

### Erreur de conversion
- Vérifiez que tous les onglets sont présents
- Vérifiez que les colonnes sont dans le bon ordre

### Données manquantes
- Vérifiez que les cellules fusionnées sont correctes
- Vérifiez que toutes les références ont des données

---

**🎯 Objectif** : Convertir automatiquement votre fichier Excel existant vers le format du système de catalogue !
