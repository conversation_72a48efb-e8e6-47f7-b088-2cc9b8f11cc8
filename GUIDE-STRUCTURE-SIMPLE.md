# 🎯 Guide - Structure Excel Simplifiée

## 📊 Nouvelle structure recommandée

### **Avant (complexe) :**
- 7 colonnes avec catégories et sous-catégories
- Structure complexe avec cellules fusionnées
- Difficile à maintenir

### **Maintenant (simple) :**
- **4 colonnes seulement** : Reference, Nom, Description, Equivalent, Image
- **Nom de la feuille = Catégorie automatique**
- **Une ligne = un produit**
- **Facile à maintenir et modifier**

## 🔧 Structure des colonnes

| Colonne | Contenu | Obligatoire | Exemple |
|---------|---------|-------------|---------|
| **Reference** | Code unique du produit | ✅ OUI | DFA0011, DFA0018 |
| **Nom** | Nom descriptif du produit | ✅ OUI | Filtre RENAULT - Clio |
| **Description** | Applications et détails | ❌ NON | Clio 1.9 D (64 HP), Clio 1.8i RT... |
| **Equivalent** | Références équivalentes | ❌ NON | RENAULT: 7701034705, ASZ: ASZ1011... |
| **Image** | Nom du fichier image | ❌ NON | DFA0011.jpg |

## 📁 Organisation par feuilles Excel

### **Feuille "Filtre Air"**
```
Reference | Nom | Description | Equivalent | Image
DFA0011 | Filtre RENAULT - Clio | Clio 1.9 D (64 HP), Clio 1.8i RT... | RENAULT: 7701034705... | DFA0011.jpg
DFA0018 | Filtre CITROEN-PEUGEOT | CITROEN AX, BX, C15 E... | PEUGEOT: 123456... | DFA0018.jpg
```

### **Feuille "Gasoil"**
```
Reference | Nom | Description | Equivalent | Image
DFG001 | Filtre gasoil RENAULT | Clio 1.9 D, Express 1.9 D | RENAULT: 987654... | DFG001.jpg
```

### **Feuille "Huile"**
```
Reference | Nom | Description | Equivalent | Image
DFH001 | Filtre huile RENAULT | Clio 1.9 D, Express 1.9 D | RENAULT: 456789... | DFH001.jpg
```

## 🎨 Mise en page automatique dans InDesign

### **En-têtes de page automatiques :**
- **Gauche** : Nom de la catégorie (ex: "Filtre Air")
- **Droite** : Numéro de page ou sous-titre
- **Style** : Titre principal avec votre design

### **Organisation des pages :**
1. **Page 1** : Couverture
2. **Pages 2-3** : Table des matières
3. **Pages suivantes** : Une catégorie par section
   - En-tête : "Filtre Air" (nom de la feuille)
   - 8 produits par page
   - Navigation claire

## 🚀 Workflow simplifié

### **1. Créer votre fichier Excel**
- Créez une feuille pour chaque catégorie
- Nommez les feuilles : "Filtre Air", "Gasoil", "Huile", etc.
- Ajoutez vos produits ligne par ligne

### **2. Placer le fichier**
- Renommez votre fichier en `catalogue.xlsx`
- Placez-le dans le dossier `excel/`

### **3. Convertir et générer**
```bash
# Conversion simplifiée
npm run convert-excel-simple

# Génération du catalogue
npm run generate
```

## ✅ Avantages de la nouvelle structure

### **Simplicité**
- **4 colonnes seulement** au lieu de 7
- **Pas de catégories** à saisir manuellement
- **Structure claire** et intuitive

### **Flexibilité**
- **Ajoutez des feuilles** = nouvelles catégories
- **Modifiez facilement** vos produits
- **Ordre respecté** : vos insertions sont préservées

### **Maintenance**
- **Facile à mettre à jour**
- **Pas de risque d'erreur** de catégorie
- **Structure cohérente** dans tout le fichier

## 📝 Exemple complet

### **Fichier Excel avec 3 feuilles :**

#### **Feuille "Filtre Air"**
```
Reference | Nom | Description | Equivalent | Image
DFA0011 | Filtre RENAULT - Clio | Clio 1.9 D (64 HP), Clio 1.8i RT, Initiale (90 HP), Clio 1.7 RT, Baccara (92 HP), Clio 1.8i RT RSI (95/110 HP), Express 1.9 D (65 HP) | RENAULT: 7701034705, ASZ: ASZ1011, SINFA: 22877, MECAFILTRE: EL3413, KNECHT: LX329, MISFAT: R194, MANN: C16113, FILTRON: AR282 | DFA0011.jpg
DFA0018 | Filtre CITROEN-PEUGEOT | CITROEN AX, BX, C15 E, Saxo, ZX; PEUGEOT 106, 205, 309, 405; TALBOT Horizon, Simca 1100 | PEUGEOT: 123456, ASZ: ASZ1018, SINFA: 22878, MECAFILTRE: EL3414, KNECHT: LX330, MISFAT: R195, MANN: C16114, FILTRON: AR283 | DFA0018.jpg
```

#### **Feuille "Gasoil"**
```
Reference | Nom | Description | Equivalent | Image
DFG001 | Filtre gasoil RENAULT | Clio 1.9 D, Express 1.9 D | RENAULT: 987654, ASZ: ASZ2001, SINFA: 33001 | DFG001.jpg
```

#### **Feuille "Huile"**
```
Reference | Nom | Description | Equivalent | Image
DFH001 | Filtre huile RENAULT | Clio 1.9 D, Express 1.9 D | RENAULT: 456789, ASZ: ASZ3001, SINFA: 44001 | DFH001.jpg
```

## 🔄 Résultat dans InDesign

### **Pages générées automatiquement :**
- **Page 1** : Couverture
- **Pages 2-3** : Table des matières
- **Pages 4-5** : Section "Filtre Air" (en-tête automatique)
- **Pages 6-7** : Section "Gasoil" (en-tête automatique)
- **Pages 8-9** : Section "Huile" (en-tête automatique)

### **En-têtes automatiques :**
- **Gauche** : "Filtre Air" (nom de la feuille Excel)
- **Droite** : Numéro de page
- **Style** : Titre principal avec votre design

## ⚠️ Points importants

### **Doit être respecté :**
- **Reference** : Unique et non vide
- **Nom** : Descriptif et non vide
- **Nom des feuilles** : Clairs et descriptifs

### **Peut être vide :**
- **Description** : Optionnel
- **Equivalent** : Optionnel
- **Image** : Utilisera Reference + .jpg par défaut

## 🎯 Résultat final

Vous obtenez un **système ultra-simple** qui :
- **Lit automatiquement** vos feuilles Excel
- **Génère des en-têtes** avec le nom de la feuille
- **Organise par catégories** automatiquement
- **Préserve l'ordre** de vos produits
- **Facilite la maintenance** de votre catalogue

---

**🚀 Objectif** : Structure Excel simple et efficace pour un catalogue professionnel !
