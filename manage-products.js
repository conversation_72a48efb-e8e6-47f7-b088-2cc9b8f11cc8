const XLSX = require('xlsx');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const readline = require('readline');

class ProductManager {
    constructor() {
        this.excelPath = path.join(__dirname, 'excel', 'catalogue.xlsx');
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    async init() {
        // Créer le fichier Excel s'il n'existe pas
        if (!await fs.pathExists(this.excelPath)) {
            await this.createInitialExcel();
        }
    }

    async createInitialExcel() {
        const headers = ['Reference', 'Nom', 'Categorie', 'SousCategorie', 'Description', 'Equivalent', 'Image'];
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.aoa_to_sheet([headers]);
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Catalogue');
        
        await fs.ensureDir(path.dirname(this.excelPath));
        XLSX.writeFile(workbook, this.excelPath);
        console.log(chalk.green('✅ Fichier Excel initial créé'));
    }

    async addProduct() {
        try {
            console.log(chalk.blue('\n📝 Ajout d\'un nouveau produit'));
            
            const product = await this.getProductInput();
            
            // Lire le fichier Excel existant
            const workbook = XLSX.readFile(this.excelPath);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            // Ajouter le nouveau produit
            const newRow = [
                product.reference,
                product.nom,
                product.categorie,
                product.sousCategorie,
                product.description,
                product.equivalent,
                product.image
            ];
            
            data.push(newRow);
            
            // Réécrire le fichier
            const newWorksheet = XLSX.utils.aoa_to_sheet(data);
            workbook.Sheets[workbook.SheetNames[0]] = newWorksheet;
            XLSX.writeFile(workbook, this.excelPath);
            
            console.log(chalk.green('✅ Produit ajouté avec succès !'));
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de l\'ajout :'), error.message);
        }
    }

    async updateProduct() {
        try {
            console.log(chalk.blue('\n✏️ Modification d\'un produit'));
            
            // Lister les produits existants
            await this.listProducts();
            
            const reference = await this.question('Entrez la référence du produit à modifier : ');
            
            // Lire le fichier Excel
            const workbook = XLSX.readFile(this.excelPath);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            // Trouver le produit
            const productIndex = data.findIndex(row => row[0] === reference);
            if (productIndex === -1) {
                throw new Error('Produit non trouvé');
            }
            
            console.log(chalk.yellow('\nProduit trouvé. Entrez les nouvelles valeurs (laissez vide pour conserver) :'));
            
            const product = await this.getProductInput(true);
            
            // Mettre à jour les valeurs
            const currentRow = data[productIndex];
            const updatedRow = [
                product.reference || currentRow[0],
                product.nom || currentRow[1],
                product.categorie || currentRow[2],
                product.sousCategorie || currentRow[3],
                product.description || currentRow[4],
                product.equivalent || currentRow[5],
                product.image || currentRow[6]
            ];
            
            data[productIndex] = updatedRow;
            
            // Réécrire le fichier
            const newWorksheet = XLSX.utils.aoa_to_sheet(data);
            workbook.Sheets[workbook.SheetNames[0]] = newWorksheet;
            XLSX.writeFile(workbook, this.excelPath);
            
            console.log(chalk.green('✅ Produit modifié avec succès !'));
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de la modification :'), error.message);
        }
    }

    async deleteProduct() {
        try {
            console.log(chalk.blue('\n🗑️ Suppression d\'un produit'));
            
            // Lister les produits existants
            await this.listProducts();
            
            const reference = await this.question('Entrez la référence du produit à supprimer : ');
            
            // Lire le fichier Excel
            const workbook = XLSX.readFile(this.excelPath);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            // Trouver et supprimer le produit
            const productIndex = data.findIndex(row => row[0] === reference);
            if (productIndex === -1) {
                throw new Error('Produit non trouvé');
            }
            
            const confirmation = await this.question(`Êtes-vous sûr de vouloir supprimer le produit ${reference} ? (oui/non) : `);
            if (confirmation.toLowerCase() !== 'oui') {
                console.log(chalk.yellow('Suppression annulée'));
                return;
            }
            
            data.splice(productIndex, 1);
            
            // Réécrire le fichier
            const newWorksheet = XLSX.utils.aoa_to_sheet(data);
            workbook.Sheets[workbook.SheetNames[0]] = newWorksheet;
            XLSX.writeFile(workbook, this.excelPath);
            
            console.log(chalk.green('✅ Produit supprimé avec succès !'));
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de la suppression :'), error.message);
        }
    }

    async listProducts() {
        try {
            const workbook = XLSX.readFile(this.excelPath);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            if (data.length <= 1) {
                console.log(chalk.yellow('Aucun produit dans le catalogue'));
                return;
            }
            
            console.log(chalk.blue('\n📋 Liste des produits :'));
            console.log(chalk.gray('─'.repeat(100)));
            
            // En-têtes
            const headers = data[0];
            console.log(chalk.bold(`${headers[0].padEnd(12)} | ${headers[1].padEnd(30)} | ${headers[2].padEnd(15)} | ${headers[3].padEnd(15)}`));
            console.log(chalk.gray('─'.repeat(100)));
            
            // Produits
            data.slice(1).forEach((row, index) => {
                console.log(`${(row[0] || '').padEnd(12)} | ${(row[1] || '').padEnd(30)} | ${(row[2] || '').padEnd(15)} | ${(row[3] || '').padEnd(15)}`);
            });
            
            console.log(chalk.gray('─'.repeat(100)));
            console.log(chalk.blue(`Total : ${data.length - 1} produits`));
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur lors de la lecture :'), error.message);
        }
    }

    async getProductInput(isUpdate = false) {
        const product = {};
        
        product.reference = await this.question(`Référence${isUpdate ? ' (actuelle)' : ''} : `);
        if (!isUpdate || product.reference) {
            product.nom = await this.question('Nom du produit : ');
            product.categorie = await this.question('Catégorie (Filtre air, Gasoil, Huile, Habitacle, Poids lourds) : ');
            product.sousCategorie = await this.question('Sous-catégorie (Européenne, Asie, etc.) : ');
            product.description = await this.question('Description : ');
            product.equivalent = await this.question('Référence équivalente : ');
            product.image = await this.question('Nom du fichier image : ');
        }
        
        return product;
    }

    question(query) {
        return new Promise((resolve) => {
            this.rl.question(query, resolve);
        });
    }

    close() {
        this.rl.close();
    }
}

// Gestion des commandes en ligne de commande
async function main() {
    const manager = new ProductManager();
    await manager.init();
    
    const command = process.argv[2];
    
    try {
        switch (command) {
            case 'add':
                await manager.addProduct();
                break;
            case 'update':
                await manager.updateProduct();
                break;
            case 'delete':
                await manager.deleteProduct();
                break;
            case 'list':
                await manager.listProducts();
                break;
            default:
                console.log(chalk.blue('🛠️ Gestionnaire de produits Dynamic Access'));
                console.log(chalk.gray('\nCommandes disponibles :'));
                console.log(chalk.gray('  npm run add-product     - Ajouter un produit'));
                console.log(chalk.gray('  npm run update-product  - Modifier un produit'));
                console.log(chalk.gray('  npm run delete-product  - Supprimer un produit'));
                console.log(chalk.gray('  npm run list-products   - Lister tous les produits'));
                console.log(chalk.gray('  npm run generate        - Générer le catalogue'));
        }
    } catch (error) {
        console.error(chalk.red('❌ Erreur :'), error.message);
    } finally {
        manager.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = ProductManager;
